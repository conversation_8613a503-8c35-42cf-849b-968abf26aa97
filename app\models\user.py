from flask_login import UserMixin
from app import login_manager

# 简化版的用户模型（不需要数据库，基于配置文件中的密码）
class User(UserMixin):
    """用户类，实现用户认证功能"""
    
    def __init__(self, username, user_level):
        self.id = username
        self.username = username
        self.user_level = user_level
    
    def get_id(self):
        return self.id
    
    def has_permission(self, required_level):
        """检查用户是否有足够的权限"""
        level_map = {
            'limited': 1,
            'standard': 2,
            'full': 3
        }
        
        user_level_value = level_map.get(self.user_level, 0)
        required_level_value = level_map.get(required_level, 0)
        
        return user_level_value >= required_level_value


@login_manager.user_loader
def load_user(user_id):
    """从session中加载用户"""
    # 这里简化处理，实际应用中应该从数据库或缓存加载用户
    from config import Config
    
    # 反向查找用户
    for password, level in Config.USER_LEVELS.items():
        if user_id == password:  # 这里使用密码作为用户ID
            return User(user_id, level)
    
    return None 