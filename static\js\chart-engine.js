/**
 * 企业级图表引擎核心模块
 * 解决Chart.js依赖管理、图表生命周期管理、类型切换等核心问题
 * 
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-01-XX
 */

class ChartEngine {
    constructor() {
        this.charts = new Map();
        this.isChartJsReady = false;
        this.readyCallbacks = [];
        this.loadTimeout = 10000; // 10秒超时
        
        // 初始化状态检测
        this.initChartJsDetection();
    }
    
    /**
     * 初始化Chart.js就绪状态检测
     * 解决依赖加载时序问题
     */
    initChartJsDetection() {
        const checkChartJs = () => {
            if (typeof Chart !== 'undefined' && Chart.register) {
                this.isChartJsReady = true;
                this.executeReadyCallbacks();
                console.log('✅ Chart.js引擎就绪');
                return true;
            }
            return false;
        };
        
        // 立即检测
        if (checkChartJs()) return;
        
        // 定时检测机制（企业级容错策略）
        let attempts = 0;
        const maxAttempts = 50; // 最多检测5秒
        
        const detector = setInterval(() => {
            attempts++;
            
            if (checkChartJs()) {
                clearInterval(detector);
            } else if (attempts >= maxAttempts) {
                clearInterval(detector);
                console.error('❌ Chart.js加载超时，请检查依赖');
                this.handleChartJsLoadError();
            }
        }, 100);
    }
    
    /**
     * 处理Chart.js加载失败的企业级降级策略
     */
    handleChartJsLoadError() {
        // 显示友好的错误提示
        this.readyCallbacks.forEach(callback => {
            try {
                callback.reject(new Error('图表引擎加载失败'));
            } catch (e) {
                console.error('回调执行失败:', e);
            }
        });
        this.readyCallbacks = [];
    }
    
    /**
     * 等待Chart.js就绪的Promise接口
     * @returns {Promise} 就绪状态Promise
     */
    whenReady() {
        return new Promise((resolve, reject) => {
            if (this.isChartJsReady) {
                resolve();
            } else {
                this.readyCallbacks.push({ resolve, reject });
                
                // 设置超时保护
                setTimeout(() => {
                    reject(new Error('图表引擎初始化超时'));
                }, this.loadTimeout);
            }
        });
    }
    
    /**
     * 执行所有等待回调
     */
    executeReadyCallbacks() {
        this.readyCallbacks.forEach(callback => {
            try {
                callback.resolve();
            } catch (e) {
                console.error('回调执行失败:', e);
            }
        });
        this.readyCallbacks = [];
    }
    
    /**
     * 创建图表实例（企业级工厂方法）
     * @param {string} canvasId - Canvas元素ID
     * @param {Object} config - 图表配置
     * @returns {Promise<Chart>} 图表实例Promise
     */
    async createChart(canvasId, config) {
        try {
            // 等待Chart.js就绪
            await this.whenReady();
            
            // 验证Canvas元素
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                throw new Error(`Canvas元素未找到: ${canvasId}`);
            }
            
            // 销毁已存在的图表实例
            this.destroyChart(canvasId);
            
            // 创建新图表实例
            const chartInstance = new Chart(canvas, config);
            
            // 注册到管理器
            this.charts.set(canvasId, {
                instance: chartInstance,
                config: { ...config },
                canvas: canvas,
                createdAt: new Date()
            });
            
            console.log(`✅ 图表创建成功: ${canvasId}`);
            return chartInstance;
            
        } catch (error) {
            console.error(`❌ 图表创建失败: ${canvasId}`, error);
            throw error;
        }
    }
    
    /**
     * 销毁图表实例（内存管理）
     * @param {string} canvasId - Canvas元素ID
     */
    destroyChart(canvasId) {
        const chartData = this.charts.get(canvasId);
        if (chartData && chartData.instance) {
            try {
                chartData.instance.destroy();
                console.log(`🗑️ 图表已销毁: ${canvasId}`);
            } catch (error) {
                console.warn(`图表销毁警告: ${canvasId}`, error);
            }
        }
        this.charts.delete(canvasId);
    }
    
    /**
     * 获取图表实例
     * @param {string} canvasId - Canvas元素ID
     * @returns {Chart|null} 图表实例
     */
    getChart(canvasId) {
        const chartData = this.charts.get(canvasId);
        return chartData ? chartData.instance : null;
    }
    
    /**
     * 切换图表类型（企业级类型管理）
     * @param {string} canvasId - Canvas元素ID
     * @param {string} newType - 新的图表类型
     * @returns {Promise<boolean>} 切换是否成功
     */
    async switchChartType(canvasId, newType) {
        try {
            const chartData = this.charts.get(canvasId);
            if (!chartData || !chartData.instance) {
                throw new Error(`图表实例未找到: ${canvasId}`);
            }
            
            const chart = chartData.instance;
            const oldType = chart.config.type;
            
            if (oldType === newType) {
                console.log(`图表类型无变化: ${newType}`);
                return true;
            }
            
            // 特殊处理：饼图转换
            if (newType === 'pie' || oldType === 'pie') {
                await this.handlePieChartConversion(canvasId, newType);
            } else {
                // 标准类型切换
                chart.config.type = newType;
                this.applyTypeSpecificSettings(chart, newType);
                chart.update('active');
            }
            
            console.log(`✅ 图表类型切换成功: ${oldType} → ${newType}`);
            return true;
            
        } catch (error) {
            console.error(`❌ 图表类型切换失败: ${canvasId}`, error);
            return false;
        }
    }
    
    /**
     * 处理饼图转换的特殊逻辑
     * @param {string} canvasId - Canvas元素ID
     * @param {string} newType - 新的图表类型
     */
    async handlePieChartConversion(canvasId, newType) {
        const chartData = this.charts.get(canvasId);
        const chart = chartData.instance;
        const originalConfig = chartData.config;
        
        if (newType === 'pie') {
            // 转换为饼图数据格式
            chart.data = this.convertToPieData(chart.data, '数据分布');
            chart.options = this.getPieChartOptions('数据分布');
        } else {
            // 从饼图恢复到其他类型
            chart.data = { ...originalConfig.data };
            chart.options = this.getStandardChartOptions(originalConfig.options.plugins?.title?.text || '图表');
        }
        
        chart.config.type = newType;
        this.applyTypeSpecificSettings(chart, newType);
        chart.update('active');
    }
    
    /**
     * 应用类型特定的设置
     * @param {Chart} chart - 图表实例
     * @param {string} type - 图表类型
     */
    applyTypeSpecificSettings(chart, type) {
        switch (type) {
            case 'line':
                chart.data.datasets.forEach(dataset => {
                    dataset.fill = false;
                    dataset.tension = 0.4;
                });
                break;
            case 'bar':
                chart.data.datasets.forEach(dataset => {
                    dataset.fill = true;
                });
                break;
            case 'pie':
                // 饼图特殊处理在handlePieChartConversion中完成
                break;
        }
    }
    
    /**
     * 数据转换工具：标准格式转饼图格式
     * @param {Object} originalData - 原始数据
     * @param {string} title - 图表标题
     * @returns {Object} 饼图数据格式
     */
    convertToPieData(originalData, title) {
        if (!originalData || !originalData.datasets || originalData.datasets.length < 2) {
            return originalData;
        }
        
        const dataset1 = originalData.datasets[0];
        const dataset2 = originalData.datasets[1];
        const total1 = dataset1.data.reduce((sum, val) => sum + val, 0);
        const total2 = dataset2.data.reduce((sum, val) => sum + val, 0);
        
        return {
            labels: [dataset1.label, dataset2.label],
            datasets: [{
                label: title,
                data: [total1, total2],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 2
            }]
        };
    }
    
    /**
     * 获取饼图选项配置
     * @param {string} title - 图表标题
     * @returns {Object} 饼图选项
     */
    getPieChartOptions(title) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title
                },
                legend: {
                    position: 'right'
                }
            }
        };
    }
    
    /**
     * 获取标准图表选项配置
     * @param {string} title - 图表标题
     * @returns {Object} 标准图表选项
     */
    getStandardChartOptions(title) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: title
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };
    }
    
    /**
     * 批量销毁所有图表（资源清理）
     */
    destroyAll() {
        this.charts.forEach((chartData, canvasId) => {
            this.destroyChart(canvasId);
        });
        console.log('🗑️ 所有图表已清理');
    }
    
    /**
     * 获取引擎状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isReady: this.isChartJsReady,
            chartCount: this.charts.size,
            charts: Array.from(this.charts.keys()),
            pendingCallbacks: this.readyCallbacks.length
        };
    }
}

// 导出单例实例
window.ChartEngine = window.ChartEngine || new ChartEngine();

// AMD/CommonJS兼容
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartEngine;
} 