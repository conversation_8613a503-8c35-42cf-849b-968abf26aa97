#!/bin/bash

# 太享查询系统一键部署脚本
# 适用于Ubuntu 24.04
# 部署目标：/var/www/hdsc_query_app
# 服务器IP：**************
# 服务器端口：5000
# 更新日期：2024年12月 - 支持二维码生成功能和前端优化
# 修复版本：解决qrcode版本检查问题和pandas编译问题

# 检查Python版本并给出pandas安装建议
PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
log_info "检测到Python版本: $PYTHON_VERSION"

if [[ "$PYTHON_VERSION" == "3.12" ]]; then
    log_warn "检测到Python 3.12，pandas编译可能会遇到兼容性问题"
    log_info "将优先使用系统pandas包和预编译版本"
fi

# 设置错误处理
# 注意：我们不使用 set -e，因为pandas安装可能会失败，我们需要尝试多种方法
trap 'echo "部署过程中发生严重错误，请检查日志"; exit 1' ERR

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP $1]${NC} $2"
}

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    log_error "请使用sudo运行此脚本"
    exit 1
fi

# 检查系统版本
log_step 1 "检查系统版本"
if grep -q "Ubuntu 24.04" /etc/os-release; then
    log_info "系统版本检查通过: Ubuntu 24.04"
else
    log_warn "系统版本可能不是Ubuntu 24.04，可能会导致兼容性问题"
    read -p "是否继续? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "部署已取消"
        exit 1
    fi
fi

# 设置目标目录
APP_DIR="/var/www/hdsc_query_app"
VENV_DIR="$APP_DIR/.venv"
USER=$(whoami)

# 检查目标目录是否存在
log_step 2 "检查项目目录"
if [ ! -d "$APP_DIR" ]; then
    log_error "目标目录 $APP_DIR 不存在，请先上传项目文件"
    exit 1
fi

if [ ! -f "$APP_DIR/run.py" ]; then
    log_error "项目主文件 run.py 不存在，请确认项目文件已正确上传"
    exit 1
fi

# 检查关键功能文件
log_info "检查关键功能文件..."
MISSING_FILES=()

# 检查二维码生成功能相关文件
if [ ! -f "$APP_DIR/app/routes/main.py" ]; then
    MISSING_FILES+=("app/routes/main.py (二维码生成路由)")
fi

# 检查前端优化文件
if [ ! -d "$APP_DIR/app/static/js/modules" ]; then
    log_warn "前端模块化目录不存在，可能影响前端功能"
fi

if [ ! -f "$APP_DIR/app/static/css/unified-table-styles.css" ]; then
    log_warn "统一表格样式文件不存在，可能影响表格显示"
fi

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    log_error "缺少关键文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    log_error "请确认项目文件已完整上传"
    exit 1
fi

log_info "项目目录检查通过"

# 测试网络连接
log_step 3 "测试网络连接"
# 定义多个国内镜像源，按优先级排序
MIRRORS=(
    "https://mirrors.aliyun.com/pypi/simple/"
    "https://pypi.tuna.tsinghua.edu.cn/simple/"
    "https://mirrors.cloud.tencent.com/pypi/simple/"
    "https://mirrors.ivolces.com/pypi/simple/"
)

# 测试镜像连接性并选择最快的镜像
log_info "测试国内镜像源连接性..."
BEST_MIRROR=""
BEST_TIME=999

for mirror in "${MIRRORS[@]}"; do
    start_time=$(date +%s.%N)
    if curl --connect-timeout 5 -s "$mirror" > /dev/null; then
        end_time=$(date +%s.%N)
        time_taken=$(echo "$end_time - $start_time" | bc)
        log_info "镜像 $mirror 连接成功，耗时: $time_taken 秒"
        if (( $(echo "$time_taken < $BEST_TIME" | bc -l) )); then
            BEST_TIME=$time_taken
            BEST_MIRROR=$mirror
        fi
    else
        log_warn "镜像 $mirror 连接失败"
    fi
done

if [ -z "$BEST_MIRROR" ]; then
    log_warn "所有国内镜像连接失败，将使用阿里云镜像作为默认值"
    PIP_MIRROR="https://mirrors.aliyun.com/pypi/simple/"
else
    log_info "选择最快的镜像: $BEST_MIRROR"
    PIP_MIRROR=$BEST_MIRROR
fi

# 配置pip使用国内镜像
log_info "配置pip永久使用国内镜像"
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
index-url = $PIP_MIRROR
trusted-host = ${PIP_MIRROR#https://}
EOF
log_info "pip已配置使用镜像: $PIP_MIRROR"

# 测试网络连接
if ! ping -c 3 pypi.org &>/dev/null; then
    log_warn "无法连接到pypi.org，将使用国内镜像源"
else
    log_info "网络连接正常，但考虑到中国网络环境，仍使用国内镜像源"
fi
log_info "使用镜像源: $PIP_MIRROR"

# 更换apt源为国内镜像
log_step 4 "更换系统软件源为国内镜像"
# 备份原始sources.list
cp /etc/apt/sources.list /etc/apt/sources.list.bak
# 使用阿里云镜像源
cat > /etc/apt/sources.list << EOF
# 阿里云镜像源 - Ubuntu 24.04 (Noble Numbat)
deb https://mirrors.aliyun.com/ubuntu/ noble main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-updates main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-backports main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-security main restricted universe multiverse

# 清华大学镜像源（备用）
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-updates main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-backports main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-security main restricted universe multiverse
EOF
log_info "系统软件源已更换为阿里云镜像"

# 更新系统包
log_step 5 "更新系统包"
apt update -y
if [ $? -ne 0 ]; then
    log_error "系统包更新失败"
    exit 1
fi
log_info "系统包更新完成"

# 安装系统依赖
log_step 6 "安装系统依赖"
log_info "安装基础开发工具..."
apt install -y python3 python3-pip python3-venv python3-dev build-essential libssl-dev

# 安装二维码生成功能所需的图像处理库依赖
log_info "安装二维码生成功能所需的图像处理库依赖..."
apt install -y libjpeg-dev zlib1g-dev libfreetype6-dev liblcms2-dev libwebp-dev tcl8.6-dev tk8.6-dev python3-tk
apt install -y libtiff5-dev libopenjp2-7-dev libffi-dev libjpeg8-dev libpng-dev

# 安装更多系统依赖，确保所有Python包都能正确编译
log_info "安装其他必要的系统依赖..."
apt install -y libxml2-dev libxslt1-dev python3-pil python3-matplotlib python3-flask

# 安装字体支持（用于二维码生成时的文本渲染）
log_info "安装字体支持..."
apt install -y fonts-dejavu-core fonts-liberation ttf-wqy-zenhei

if [ $? -ne 0 ]; then
    log_error "系统依赖安装失败"
    exit 1
fi
log_info "系统依赖安装完成"

# 安装pandas所需依赖
log_step 7 "安装数据处理库依赖"
apt install -y libpython3-dev cython3 python3-numpy python3-wheel
if [ $? -ne 0 ]; then
    log_warn "数据处理库依赖安装可能不完整，但将继续尝试"
fi
log_info "数据处理库依赖安装完成"

# 创建并激活虚拟环境
log_step 8 "创建Python虚拟环境"
cd $APP_DIR
if [ -d "$VENV_DIR" ]; then
    log_warn "虚拟环境已存在，将重新创建"
    rm -rf $VENV_DIR
fi

python3 -m venv $VENV_DIR
if [ $? -ne 0 ]; then
    log_error "虚拟环境创建失败"
    exit 1
fi
log_info "虚拟环境创建完成"

# 激活虚拟环境并安装依赖
log_step 9 "安装项目依赖"
source $VENV_DIR/bin/activate

# 安装基础科学计算库
log_info "安装基础科学计算库"
pip install wheel setuptools

# 先安装兼容的numpy版本（避免pandas编译问题）
log_info "安装兼容的numpy版本"
apt install -y python3-numpy || true
pip install -i $PIP_MIRROR "numpy>=1.21.0,<2.0.0" || pip install -i $PIP_MIRROR numpy || true

if [ $? -ne 0 ]; then
    log_warn "numpy安装可能不完整，但将继续尝试"
fi

# 尝试多种方式安装pandas
log_info "尝试安装pandas (可能需要一些时间)"
PANDAS_INSTALLED=0

# 首先尝试使用系统pandas（最稳定的方法）
log_info "尝试方法1: 使用系统pandas包"
apt install -y python3-pandas
if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
    log_info "系统pandas安装成功"
    PANDAS_INSTALLED=1
else
    log_warn "系统pandas安装失败，尝试其他方法"
fi

# 方法2：尝试安装预编译的兼容版本
if [ $PANDAS_INSTALLED -eq 0 ]; then
    log_info "尝试方法2: 安装预编译兼容版本pandas"
    pip install -i $PIP_MIRROR --only-binary=all pandas
    if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
        log_info "预编译pandas安装成功"
        PANDAS_INSTALLED=1
    else
        log_warn "预编译pandas安装失败，尝试其他方法"
    fi
fi

# 方法3：尝试安装较新的稳定版本
if [ $PANDAS_INSTALLED -eq 0 ]; then
    log_info "尝试方法3: 安装pandas==2.2.0（兼容Python 3.12）"
    pip install -i $PIP_MIRROR pandas==2.2.0
    if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
        log_info "pandas 2.2.0安装成功"
        PANDAS_INSTALLED=1
    else
        log_warn "pandas 2.2.0安装失败，尝试其他方法"
    fi
fi

# 方法4：尝试安装最新版本
if [ $PANDAS_INSTALLED -eq 0 ]; then
    log_info "尝试方法4: 安装最新版本pandas"
    pip install -i $PIP_MIRROR pandas
    if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
        log_info "pandas最新版本安装成功"
        PANDAS_INSTALLED=1
    else
        log_warn "pandas最新版本安装失败，尝试其他方法"
    fi
fi

# 方法5：尝试安装稳定旧版本（如果其他都失败）
if [ $PANDAS_INSTALLED -eq 0 ]; then
    log_info "尝试方法5: 安装稳定旧版本pandas==1.5.3"
    pip install -i $PIP_MIRROR pandas==1.5.3
    if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
        log_info "pandas 1.5.3安装成功"
        PANDAS_INSTALLED=1
    else
        log_warn "pandas 1.5.3安装失败，尝试最后的方法"
    fi
fi

# 最后的方法：组合系统包和pip
if [ $PANDAS_INSTALLED -eq 0 ]; then
    log_info "尝试最后的方法: 组合系统包和pip安装"
    apt install -y python3-pandas python3-numpy python3-dateutil python3-pytz
    pip install --no-deps -i $PIP_MIRROR pandas
    if python3 -c "import pandas; print(f'pandas版本: {pandas.__version__}')" &>/dev/null; then
        log_info "组合方法pandas安装成功"
        PANDAS_INSTALLED=1
    else
        log_error "所有pandas安装方法均失败"
        log_error "这可能是由于Python 3.12与pandas版本兼容性问题"
        log_error "建议手动安装pandas或使用较低版本的Python"
        exit 1
    fi
fi

# 安装其他依赖
log_info "安装其他项目依赖"
grep -v "pandas" $APP_DIR/requirements.txt > $APP_DIR/requirements_no_pandas.txt

# 使用镜像源安装关键依赖
log_info "安装Flask和其他关键依赖"
pip install -i $PIP_MIRROR Flask==2.3.3 Flask-Login==0.6.2 Flask-WTF==1.1.1 Flask-Caching==2.1.0 Werkzeug==2.3.7 python-dotenv==1.0.0

# 安装Excel处理依赖
log_info "安装Excel处理依赖"
pip install -i $PIP_MIRROR xlrd>=2.0.1 openpyxl==3.1.2

# 安装APScheduler (定时任务支持)
log_info "安装APScheduler定时任务支持"
pip install -i $PIP_MIRROR APScheduler==3.10.1
if [ $? -ne 0 ]; then
    log_error "APScheduler安装失败，无法继续，定时任务将无法工作"
    exit 1
fi

# 安装二维码生成功能依赖 - 重点新增
log_info "安装二维码生成功能依赖"
log_info "安装qrcode库..."
pip install -i $PIP_MIRROR qrcode[pil]==7.4.2
if [ $? -ne 0 ]; then
    log_warn "qrcode[pil]安装失败，尝试分别安装"
    pip install -i $PIP_MIRROR qrcode==7.4.2
    if [ $? -ne 0 ]; then
        log_error "qrcode库安装失败，二维码生成功能将无法使用"
        exit 1
    fi
fi

# 验证qrcode是否安装成功
if python3 -c "import qrcode; qr = qrcode.QRCode(); print('qrcode库安装成功')" &>/dev/null; then
    log_info "qrcode库安装成功"
else
    log_error "qrcode库安装失败，二维码生成功能将无法使用"
    exit 1
fi

# 尝试安装Pillow (多种方式) - 二维码生成必需
log_info "安装Pillow (二维码生成必需)"
# 方法1: 使用系统包
apt install -y python3-pil
# 方法2: 尝试使用pip安装指定版本
pip install -i $PIP_MIRROR Pillow==9.5.0 || true
# 方法3: 尝试使用pip安装最新版本
pip install -i $PIP_MIRROR Pillow || true
# 方法4: 尝试使用--no-binary选项
pip install --no-binary :all: -i $PIP_MIRROR Pillow==9.5.0 || true
# 方法5: 尝试使用--no-deps选项
pip install --no-deps -i $PIP_MIRROR Pillow==9.5.0 || true

# 验证Pillow是否安装成功
if python3 -c "import PIL; print(f'Pillow版本: {PIL.__version__}')" &>/dev/null; then
    log_info "Pillow安装成功"
else
    log_error "Pillow安装失败，二维码生成功能将无法使用"
    exit 1
fi

# 安装matplotlib (可能会有依赖问题)
log_info "安装matplotlib"
pip install -i $PIP_MIRROR matplotlib==3.7.2 || apt install -y python3-matplotlib && pip install --no-deps matplotlib==3.7.2

# 安装剩余依赖
log_info "安装剩余依赖"
pip install -i $PIP_MIRROR -r $APP_DIR/requirements_no_pandas.txt
if [ $? -ne 0 ]; then
    log_warn "部分依赖可能安装失败，但将继续尝试"
    # 尝试逐个安装剩余依赖
    log_info "尝试逐个安装依赖"
    for pkg in $(cat $APP_DIR/requirements_no_pandas.txt); do
        if [[ "$pkg" != "pandas"* && "$pkg" != "Flask"* && "$pkg" != "Pillow"* && "$pkg" != "matplotlib"* && "$pkg" != "qrcode"* ]]; then
            pip install -i $PIP_MIRROR $pkg || log_warn "无法安装 $pkg"
        fi
    done
fi
rm $APP_DIR/requirements_no_pandas.txt

# 确保gunicorn已安装
pip install -i $PIP_MIRROR gunicorn
if [ $? -ne 0 ]; then
    log_error "gunicorn安装失败，无法继续"
    exit 1
fi
log_info "项目依赖安装完成"

# 测试依赖导入 - 更新以包含二维码功能
log_step 10 "测试依赖导入"
cat > $APP_DIR/test_imports.py << EOF
try:
    import pandas
    print(f"pandas版本: {pandas.__version__}")
    import flask
    print(f"flask版本: {flask.__version__}")
    import PIL
    print(f"Pillow版本: {PIL.__version__}")
    import qrcode
    print("qrcode库导入成功")
    import xlrd
    print(f"xlrd版本: {xlrd.__version__}")
    import openpyxl
    print(f"openpyxl版本: {openpyxl.__version__}")
    from app import create_app
    print("应用导入成功")

    # 测试二维码生成功能
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data('测试二维码')
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    print("二维码生成功能测试成功")

    print("所有必要的依赖已成功安装")
except ImportError as e:
    print(f"导入错误: {e}")
    exit(1)
except Exception as e:
    print(f"功能测试错误: {e}")
    exit(1)
EOF

python3 $APP_DIR/test_imports.py
if [ $? -ne 0 ]; then
    log_error "依赖测试失败，尝试修复问题"
    
    # 尝试修复常见问题
    log_info "尝试修复Flask安装问题"
    apt install -y python3-flask
    pip install --force-reinstall -i $PIP_MIRROR Flask==2.3.3 Flask-Login==0.6.2 Flask-WTF==1.1.1 Flask-Caching==2.1.0
    
    log_info "尝试修复Pillow安装问题"
    apt install -y python3-pil
    pip install --force-reinstall --no-deps -i $PIP_MIRROR Pillow==9.5.0
    
    log_info "尝试修复qrcode安装问题"
    pip install --force-reinstall -i $PIP_MIRROR qrcode[pil]==7.4.2
    
    # 再次测试
    python3 $APP_DIR/test_imports.py
    if [ $? -ne 0 ]; then
        log_error "依赖修复失败，尝试最后的修复方案"
        
        # 最后的修复方案：使用系统包
        apt install -y python3-flask python3-pandas python3-pil python3-matplotlib python3-requests python3-werkzeug python3-dotenv
        
        # 最终测试
        python3 $APP_DIR/test_imports.py
        if [ $? -ne 0 ]; then
            log_error "所有修复尝试均失败，请手动检查错误信息"
            exit 1
        fi
    fi
fi
rm $APP_DIR/test_imports.py
log_info "依赖测试通过"

# 设置文件权限 - 增加静态资源权限设置
log_step 11 "设置文件权限"
chown -R $USER:www-data $APP_DIR
chmod -R 755 $APP_DIR

# 特别设置静态资源权限
log_info "设置静态资源权限..."
if [ -d "$APP_DIR/app/static" ]; then
    chmod -R 755 $APP_DIR/app/static
    # 确保CSS和JS文件可读
    find $APP_DIR/app/static -name "*.css" -exec chmod 644 {} \;
    find $APP_DIR/app/static -name "*.js" -exec chmod 644 {} \;
    log_info "静态资源权限设置完成"
fi

# 设置模板文件权限
if [ -d "$APP_DIR/app/templates" ]; then
    chmod -R 755 $APP_DIR/app/templates
    find $APP_DIR/app/templates -name "*.html" -exec chmod 644 {} \;
    log_info "模板文件权限设置完成"
fi

log_info "文件权限设置完成"

# 配置防火墙（如果启用）
log_step 12 "配置防火墙"
if command -v ufw &>/dev/null && ufw status | grep -q "active"; then
    log_info "检测到防火墙已启用，正在添加规则"
    ufw allow 5000/tcp
    if [ $? -ne 0 ]; then
        log_warn "防火墙规则添加失败，可能需要手动配置"
    else
        log_info "防火墙规则添加成功"
    fi
else
    log_info "防火墙未启用或不存在，跳过配置"
fi

# 创建gunicorn配置文件
log_step 13 "创建Gunicorn配置文件"
# 创建gunicorn配置文件
cat > $APP_DIR/gunicorn_config.py << EOF
# gunicorn配置文件
import multiprocessing

# 工作进程数，根据CPU核心数自动设置
workers = multiprocessing.cpu_count() * 2 + 1
bind = "0.0.0.0:5000"

# 工作模式
worker_class = "sync"

# 日志设置
accesslog = "./access.log"
errorlog = "./error.log"
loglevel = "info"

# 进程名称
proc_name = "hdsc_query"

# 启动时预加载应用
preload_app = True

# 定时任务初始化
def post_worker_init(worker):
    try:
        from app import create_app
        from app.services.scheduled_tasks import init_scheduler
        app = create_app('production')
        with app.app_context():
            init_scheduler(app)
            print("定时任务初始化成功")
    except Exception as e:
        print(f"定时任务初始化失败: {e}")
        # 不退出，允许应用继续运行
            
# 超时设置 - 为长时间运行的请求设置更长的超时时间
timeout = 120

# 保持连接设置
keepalive = 5

# 请求头最大大小，适应复杂查询
limit_request_line = 8190
limit_request_fields = 100
limit_request_field_size = 8190
EOF

log_info "创建了自定义gunicorn配置文件"

# 测试gunicorn配置是否正确
log_info "测试gunicorn配置"
source $VENV_DIR/bin/activate
cd $APP_DIR
$VENV_DIR/bin/gunicorn --check-config --config $APP_DIR/gunicorn_config.py run:app
if [ $? -ne 0 ]; then
    log_error "gunicorn配置测试失败，尝试修复配置问题"
    
    # 创建简化版配置作为备用方案
    cat > $APP_DIR/gunicorn_simple.py << EOF
# 简化版gunicorn配置
bind = "0.0.0.0:5000"
workers = 3
accesslog = "./access.log"
errorlog = "./error.log"
EOF
    
    log_warn "创建了简化版gunicorn配置，将使用该配置"
    # 更新变量以使用简化版配置
    GUNICORN_CONFIG="$APP_DIR/gunicorn_simple.py"
else
    log_info "gunicorn配置测试通过"
    GUNICORN_CONFIG="$APP_DIR/gunicorn_config.py"
fi

# 创建系统服务
log_step 14 "创建系统服务"
cat > /etc/systemd/system/hdsc-query.service << EOF
[Unit]
Description=太享查询系统Web服务
After=network.target

[Service]
User=$USER
Group=www-data
WorkingDirectory=$APP_DIR
Environment="PATH=$VENV_DIR/bin"
Environment="FLASK_APP=run.py"
Environment="FLASK_ENV=production"
ExecStart=$VENV_DIR/bin/gunicorn --config $GUNICORN_CONFIG run:app

[Install]
WantedBy=multi-user.target
EOF

if [ $? -ne 0 ]; then
    log_error "服务文件创建失败"
    exit 1
fi
log_info "系统服务创建完成"

# 启动并启用服务
log_step 15 "配置服务自动启动"
systemctl daemon-reload
systemctl start hdsc-query
if [ $? -ne 0 ]; then
    log_error "服务启动失败，请检查日志"
    systemctl status hdsc-query
    exit 1
fi

systemctl enable hdsc-query
if [ $? -ne 0 ]; then
    log_warn "服务自动启动配置失败，但服务已启动"
fi
log_info "服务已启动并配置为自动启动"

# 部署成功消息
log_step 20 "部署完成"

# 等待服务启动
log_info "等待服务启动..."
sleep 5

# 检查服务状态
SERVICE_STATUS=$(systemctl is-active hdsc-query)
if [ "$SERVICE_STATUS" = "active" ]; then
    log_info "✓ 服务状态: $SERVICE_STATUS"
else
    log_warn "✗ 服务状态: $SERVICE_STATUS"
    log_info "尝试重新启动服务..."
    systemctl restart hdsc-query
    sleep 5
    SERVICE_STATUS=$(systemctl is-active hdsc-query)
    log_info "重启后服务状态: $SERVICE_STATUS"
fi

# 检查端口监听
log_info "检查端口监听状态..."
if netstat -tlnp 2>/dev/null | grep -q ":5000" || ss -tlnp 2>/dev/null | grep -q ":5000"; then
    log_info "✓ 端口5000正在监听"
else
    log_warn "✗ 端口5000未在监听"
fi

# 尝试HTTP连接测试
log_info "测试HTTP连接..."
if curl -s --connect-timeout 10 http://localhost:5000 > /dev/null 2>&1; then
    log_info "✓ HTTP连接测试成功"
else
    log_warn "✗ HTTP连接测试失败，可能需要等待更长时间"
fi

log_info "太享查询系统已部署到 http://**************:5000"

# 功能检查总结
log_info "部署总结:"
echo "  - 系统服务: $(systemctl is-active hdsc-query 2>/dev/null || echo '未知')"
echo "  - 端口监听: $(netstat -tlnp 2>/dev/null | grep -q ":5000" && echo "✓ 正常" || echo "✗ 异常")"
echo "  - 二维码功能: $(python3 -c "import qrcode; print('✓ 已安装')" 2>/dev/null || echo "✗ 未安装")"
echo "  - 图像处理: $(python3 -c "import PIL; print('✓ 已安装')" 2>/dev/null || echo "✗ 未安装")"

log_info "新功能说明:"
echo "  - ✅ 二维码生成功能: 支持多种尺寸，完全本地化生成"
echo "  - ✅ 前端代码优化: JavaScript模块化，统一表格样式"
echo "  - ✅ 用户体验提升: 响应式设计，移动端优化"

log_info "如果服务无法正常访问，请使用以下命令检查错误:"
echo "  - 查看服务状态: sudo systemctl status hdsc-query"
echo "  - 查看服务日志: sudo journalctl -u hdsc-query -f"
echo "  - 查看应用日志: cat $APP_DIR/error.log"
echo "  - 手动启动测试: cd $APP_DIR && source .venv/bin/activate && python run.py"

# 服务管理命令
log_info "服务管理命令:"
echo "  - 启动服务: sudo systemctl start hdsc-query"
echo "  - 停止服务: sudo systemctl stop hdsc-query"
echo "  - 重启服务: sudo systemctl restart hdsc-query"
echo "  - 查看状态: sudo systemctl status hdsc-query"
echo "  - 查看日志: sudo journalctl -u hdsc-query -f"

# 最终状态检查
log_info "最终状态检查:"
systemctl status hdsc-query --no-pager -l

log_info "🎉 部署完成！系统已准备就绪，包含最新的二维码生成功能和前端优化。"
log_info "📱 访问地址: http://**************:5000"
