{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 数据汇总{% endblock %}

{% block styles %}
<!-- 引入企业级图表引擎 -->
<script src="{{ url_for('static', filename='js/chart-engine.js') }}"></script>
<script src="{{ url_for('static', filename='js/summary-chart-controller.js') }}"></script>

<style>
    /* 自动消失提示样式 */
    .auto-dismiss-alert {
        animation: fadeOut 0.5s ease 3s forwards;
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; height: 0; margin: 0; padding: 0; }
    }
    
    /* 表格样式增强 */
    .data-table {
        border-collapse: collapse;
        width: 100% !important;
        margin-bottom: 1rem;
    }
    
    .data-table th {
        background-color: #f2f2f2;
        font-weight: bold;
        text-align: left;
        padding: 10px;
        white-space: nowrap;
    }
    
    .data-table td {
        padding: 8px;
        vertical-align: middle;
    }
    
    /* 表格交替行颜色 */
    .data-table tbody tr:nth-child(odd) {
        background-color: #f9f9f9;
    }
    
    /* 鼠标悬停高亮效果 */
    .data-table tbody tr:hover {
        background-color: #e9f2f9;
    }
    
    /* 图表容器样式 */
    .chart-container {
        height: 300px;
        position: relative;
    }
    
    /* 响应式表格样式 */
    .table-responsive {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* DataTables响应式样式增强 */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        background-color: #007bff;
    }
    
    .dtr-data {
        word-break: break-word;
    }
    
    /* 详情行样式 */
    .dtr-details {
        width: 100%;
    }
    
    .dtr-details td {
        padding: 5px 10px;
    }
    
    /* 数据单元格折叠样式 */
    .cell-content {
        position: relative;
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        display: block;
        transition: all 0.3s ease;
        color: #333;
    }

    .cell-content:after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 30px;
        background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
        pointer-events: none;
    }

    .cell-content.expanded {
        white-space: normal;
        word-break: break-word;
        max-width: none;
        overflow: visible;
    }

    .cell-content.expanded:after {
        display: none;
    }

    /* 鼠标悬停状态 */
    .cell-content:hover {
        color: #007bff;
    }

    /* 移动端样式优化 */
    @media (max-width: 768px) {
        .cell-content {
            max-width: 120px;
        }
        
        table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
        table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
            top: 50%;
            transform: translateY(-50%);
            background-color: #007bff;
            box-shadow: 0 0 0.2rem rgba(0, 123, 255, 0.5);
        }
        
        .dtr-data {
            word-break: break-word;
            max-width: calc(100vw - 100px);
        }
        
        .table-responsive {
            max-width: 100%;
        }
        
        .data-table th, .data-table td {
            padding: 6px;
            font-size: 0.85rem;
        }
        
        .chart-container {
            height: 250px;
        }
        
        .card-body {
            padding: 0.75rem;
        }
    }
    
    /* 日期选择器样式 */
    input[type="date"] {
        position: relative;
    }
    
    /* 自定义日期选择器图标 */
    input[type="date"]::-webkit-calendar-picker-indicator {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        opacity: 1;
        cursor: pointer;
    }
    
    /* 日期选择器位置调整 */
    input[type="date"]::-webkit-datetime-edit {
        padding-right: 28px; /* 为图标留出空间 */
    }
    
    /* 调整弹出日历的位置 */
    ::-webkit-calendar-picker-indicator {
        position: relative; 
    }
    
    /* 兼容性处理 */
    input[type="date"]::-webkit-inner-spin-button {
        display: none;
    }
    
    input[type="date"]::-webkit-clear-button {
        display: none;
    }
</style>
<!-- 引入增强样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/summary-enhanced.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 引入侧边栏模板 -->
        {% include 'sidebar.html' %}

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <h1 class="h2">数据汇总分析</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportCharts()">
                            <i class="bi bi-download"></i> 导出图表
                        </button>
                    </div>
                </div>
            </div>

            <!-- 日期范围选择表单 -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.summary_view') }}" class="row g-3" onsubmit="showLoading()">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date or today }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date or today }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 查询
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <!-- 订单汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">订单月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="orderChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                    <option value="pie">饼图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orderChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总图表 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">逾期月度汇总</h5>
                            <div class="chart-controls">
                                <select class="form-select form-select-sm chart-type-selector" data-chart="overdueChart">
                                    <option value="bar">柱状图</option>
                                    <option value="line">折线图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="overdueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格区域 -->
            <div class="row">
                <!-- 订单汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">订单数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商台数/订单数量</th>
                                            <th>租赁台数/订单数量</th>
                                            <th>总台数/订单数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if order_summary and order_summary|length > 0 %}
                                            {% for item in order_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商台数|default(item.电商订单数量|default(0)) }}</td>
                                                <td>{{ item.租赁台数|default(item.租赁订单数量|default(0)) }}</td>
                                                <td>{{ (item.电商台数|default(item.电商订单数量|default(0))|int) + (item.租赁台数|default(item.租赁订单数量|default(0))|int) }}</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期汇总表格 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">逾期数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover data-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th class="dtr-control"></th> <!-- 用于响应式详情控制 -->
                                            <th>月份</th>
                                            <th>电商逾期订单数</th>
                                            <th>租赁逾期订单数</th>
                                            <th>总逾期订单数</th>
                                            <th>逾期金额</th>
                                            <th>逾期率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if overdue_summary and overdue_summary|length > 0 %}
                                            {% for item in overdue_summary %}
                                            <tr>
                                                <td class="dtr-control"></td>
                                                <td>{{ item.月份|default('未知') }}</td>
                                                <td>{{ item.电商逾期订单数量|default(0) }}</td>
                                                <td>{{ item.租赁逾期订单数量|default(0) }}</td>
                                                <td>{{ (item.电商逾期订单数量|default(0)|int) + (item.租赁逾期订单数量|default(0)|int) }}</td>
                                                <td>{{ item.逾期金额|default(0) }}</td>
                                                <td>{{ item.逾期率|default(0) }}%</td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="7" class="text-center auto-dismiss-alert">暂无数据</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 原始汇总数据表格 -->
            {% if summary_data_results and summary_data_results|length > 0 %}
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">详细汇总数据</h5>
                            <div class="btn-toolbar">
                                <div class="dropdown me-2">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dataViewSelector" data-bs-toggle="dropdown">
                                        <i class="bi bi-grid"></i> 视图
                                    </button>
                                    <ul class="dropdown-menu view-selector">
                                        <li><a class="dropdown-item active" href="#" data-view="tabs">店铺选项卡</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="table">完整表格</a></li>
                                        <li><a class="dropdown-item" href="#" data-view="cards">指标卡片</a></li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-secondary" id="exportSummaryData">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 视图内容将通过JavaScript动态加载 -->
                            <div id="summaryDataContainer"></div>
                            
                            <!-- 用于存储汇总数据的隐藏元素 -->
                            <script id="summaryData" type="application/json">
                                {
                                    "headers": {{ summary_data_headers|tojson }},
                                    "summary": {{ summary_data_results|tojson }},
                                    "timing_stats": {{ timing_stats|tojson }}
                                }
                            </script>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 全局加载指示器 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- 初始化下拉菜单功能 -->
<script>
// 确保在页面完全加载后初始化下拉菜单
window.addEventListener('load', function() {
    console.log('页面完全加载，初始化下拉菜单');
    
    try {
        // 手动为指定的下拉菜单添加点击事件
        var dataViewSelector = document.getElementById('dataViewSelector');
        if (dataViewSelector) {
            dataViewSelector.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                var dropdown = document.querySelector('.dropdown-menu.view-selector');
                if (dropdown) {
                    dropdown.classList.toggle('show');
                    dropdown.style.display = dropdown.classList.contains('show') ? 'block' : 'none';
                    dropdown.style.position = 'absolute';
                    dropdown.style.transform = 'translate3d(0px, 38px, 0px)';
                    dropdown.style.top = '0px';
                    dropdown.style.left = '0px';
                    dropdown.style.willChange = 'transform';
                }
            });
            
            // 点击其他区域时关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    var dropdown = document.querySelector('.dropdown-menu.view-selector');
                    if (dropdown && dropdown.classList.contains('show')) {
                        dropdown.classList.remove('show');
                        dropdown.style.display = 'none';
                    }
                }
            });
            
            // 处理菜单项点击
            var menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
            menuItems.forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有active类
                    menuItems.forEach(function(mi) {
                        mi.classList.remove('active');
                    });
                    
                    // 添加active类到当前项
                    this.classList.add('active');
                    
                    // 调用视图切换函数
                    if (typeof switchDataView === 'function') {
                        switchDataView(this.getAttribute('data-view'));
                    }
                    
                    // 关闭下拉菜单
                    var dropdown = document.querySelector('.dropdown-menu.view-selector');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                        dropdown.style.display = 'none';
                    }
                });
            });
        }
    } catch(e) {
        console.error('手动初始化下拉菜单失败:', e);
    }
    
    // 添加CSS样式确保下拉菜单正常显示
    var style = document.createElement('style');
    style.textContent = `
        .dropdown-menu.show {
            display: block !important;
            position: absolute !important;
            z-index: 1000 !important;
        }
    `;
    document.head.appendChild(style);
});
</script>

<!-- 引入增强型数据表格JavaScript -->
<script src="{{ url_for('static', filename='js/data-table-enhanced.js') }}"></script>
<script src="{{ url_for('static', filename='js/summary-enhanced.js') }}"></script>

<!-- 引入主JavaScript文件 -->
<script src="{{ url_for('static', filename='js/main.js') }}"></script>

<!-- 企业级图表初始化 -->
<script>
// 传递服务器数据到前端控制器
var chartData = {
    orderData: {% if order_chart_data %}{{ order_chart_data|tojson|safe }}{% else %}null{% endif %},
    overdueData: {% if overdue_chart_data %}{{ overdue_chart_data|tojson|safe }}{% else %}null{% endif %}
};

// 企业级图表控制器实例
var summaryChartController = null;

// DOM加载完成后初始化控制器
$(document).ready(function() {
    // 初始化企业级图表控制器
    summaryChartController = new SummaryChartController();
    summaryChartController.init().catch(error => {
        console.error('图表控制器初始化失败:', error);
    });
});

// 向后兼容的全局函数（企业级降级策略）
function switchChartType(chartId, chartType) {
    if (summaryChartController) {
        summaryChartController.switchChartType(chartId, chartType);
    } else {
        console.error('图表控制器未初始化');
    }
}
            type: 'bar',
            data: overdueChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度逾期趋势'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        console.log('✅ 逾期图表创建成功');
    } catch (error) {
        console.error('❌ 逾期图表创建失败:', error);
    }
}

function bindChartTypeSelectors() {
    const selectors = document.querySelectorAll('.chart-type-selector');
    console.log(`找到${selectors.length}个图表类型选择器`);
    
    selectors.forEach(function(selector) {
        selector.addEventListener('change', function() {
            const chartId = this.getAttribute('data-chart');
            const chartType = this.value;
            
            console.log(`切换图表类型: ${chartId} -> ${chartType}`);
            
            if (chartId === 'orderChart' && orderChart) {
                switchOrderChartType(chartType);
            } else if (chartId === 'overdueChart' && overdueChart) {
                switchOverdueChartType(chartType);
            }
        });
    });
}

function switchOrderChartType(newType) {
    try {
        if (newType === 'pie') {
            // 转换为饼图数据
            const dataset1 = orderChartData.datasets[0];
            const dataset2 = orderChartData.datasets[1];
            const total1 = dataset1.data.reduce((sum, val) => sum + val, 0);
            const total2 = dataset2.data.reduce((sum, val) => sum + val, 0);
            
            orderChart.data = {
                labels: [dataset1.label, dataset2.label],
                datasets: [{
                    data: [total1, total2],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ]
                }]
            };
            orderChart.config.options.scales = {};
        } else {
            // 恢复原始数据
            orderChart.data = orderChartData;
            orderChart.config.options.scales = {
                y: { beginAtZero: true }
            };
        }
        
        orderChart.config.type = newType;
        orderChart.update();
        console.log(`✅ 订单图表类型已切换为: ${newType}`);
    } catch (error) {
        console.error(`❌ 订单图表类型切换失败: ${error.message}`);
    }
}

function switchOverdueChartType(newType) {
    try {
        overdueChart.config.type = newType;
        
        if (newType === 'line') {
            overdueChart.data.datasets[0].fill = false;
        } else {
            overdueChart.data.datasets[0].fill = true;
        }
        
        overdueChart.update();
        console.log(`✅ 逾期图表类型已切换为: ${newType}`);
    } catch (error) {
        console.error(`❌ 逾期图表类型切换失败: ${error.message}`);
    }
}

// 初始化图表和表格
$(document).ready(function() {
    console.log('🚀 页面初始化开始...');
    
    // 等待DOM完全加载
    setTimeout(function() {
        console.log('📊 开始初始化图表...');
        
        // 强制初始化图表，无论数据如何
        forceInitCharts();
        
        // 初始化数据表格
        if (typeof initDataTables === 'function') {
            console.log('📋 初始化数据表格');
            initDataTables();
        }
        
        console.log('✅ 页面初始化完成');
    }, 500);
});

// 强制初始化图表 - 确保显示
function forceInitCharts() {
    console.log('🔧 强制初始化图表开始...');
    
    // 检查Chart.js是否加载
    if (typeof Chart === 'undefined') {
        console.error('❌ Chart.js未加载！');
        return;
    }
    
    // 强制初始化订单图表
    initOrderChart();
    
    // 强制初始化逾期图表
    initOverdueChart();
    
    // 设置图表类型选择器
    setupChartTypeSelectors();
    
    console.log('✅ 强制图表初始化完成');
}

// 初始化订单图表
function initOrderChart() {
    console.log('🔧 初始化订单图表...');
    
    const canvas = document.getElementById('orderChart');
    if (!canvas) {
        console.error('❌ 找不到订单图表Canvas元素');
        return;
    }
    
    // 销毁现有图表
    if (orderChart) {
        orderChart.destroy();
    }
    
    // 确保有数据
    if (!orderChartData || !orderChartData.labels || orderChartData.labels.length === 0) {
        console.log('📊 使用默认订单数据');
        orderChartData = {
            labels: ['2025-01', '2025-02', '2025-03', '2025-04'],
            datasets: [
                {
                    label: '电商订单',
                    data: [120, 150, 180, 200],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: '租赁订单',
                    data: [80, 90, 70, 85],
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    fill: false
                }
            ]
        };
    }
    
    try {
        const ctx = canvas.getContext('2d');
        orderChart = new Chart(ctx, {
            type: 'line',
            data: orderChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度订单数量趋势'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        console.log('✅ 订单图表初始化成功');
    } catch(err) {
        console.error('❌ 订单图表初始化失败:', err);
    }
}

// 初始化逾期图表
function initOverdueChart() {
    console.log('🔧 初始化逾期图表...');
    
    const canvas = document.getElementById('overdueChart');
    if (!canvas) {
        console.error('❌ 找不到逾期图表Canvas元素');
        return;
    }
    
    // 销毁现有图表
    if (overdueChart) {
        overdueChart.destroy();
    }
    
    // 确保有数据
    if (!overdueChartData || !overdueChartData.labels || overdueChartData.labels.length === 0) {
        console.log('📊 使用默认逾期数据');
        overdueChartData = {
            labels: ['2025-01', '2025-02', '2025-03', '2025-04'],
            datasets: [
                {
                    label: '逾期订单数',
                    data: [15, 20, 12, 18],
                    backgroundColor: 'rgba(255, 159, 64, 0.6)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 2,
                    fill: true
                }
            ]
        };
    }
    
    try {
        const ctx = canvas.getContext('2d');
        overdueChart = new Chart(ctx, {
            type: 'bar',
            data: overdueChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度逾期趋势'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        console.log('✅ 逾期图表初始化成功');
    } catch(err) {
        console.error('❌ 逾期图表初始化失败:', err);
    }
}

// 设置图表类型选择器
function setupChartTypeSelectors() {
    console.log('🔧 设置图表类型选择器...');
    var typeSelectors = document.querySelectorAll('.chart-type-selector');
    console.log(`找到${typeSelectors.length}个图表类型选择器`);
    
    typeSelectors.forEach(function(selector, index) {
        // 移除现有事件监听器（如果有）
        selector.removeEventListener('change', handleChartTypeChange);
        
        // 添加新的事件监听器
        selector.addEventListener('change', handleChartTypeChange);
        console.log(`✅ 选择器 #${index+1} 事件绑定完成`);
    });
}

// 图表类型变更处理函数
function handleChartTypeChange(event) {
    const selector = event.target;
    const chartId = selector.getAttribute('data-chart');
    const chartType = selector.value;
    
    console.log(`🔄 切换图表类型: ${chartId} -> ${chartType}`);
    
    try {
        if (chartId === 'orderChart' && orderChart) {
            // 特殊处理饼图
            if (chartType === 'pie') {
                // 为饼图重新组织数据
                const pieData = convertToPieData(orderChartData, '订单分布');
                orderChart.data = pieData;
                orderChart.config.type = chartType;
                orderChart.config.options = getPieChartOptions('订单分布');
            } else {
                // 恢复原始数据结构
                orderChart.data = orderChartData;
                orderChart.config.type = chartType;
                orderChart.config.options = getStandardChartOptions('月度订单数量趋势');
            }
            orderChart.update('active');
            console.log(`✅ 订单图表类型已切换为: ${chartType}`);
            
        } else if (chartId === 'overdueChart' && overdueChart) {
            overdueChart.config.type = chartType;
            
            // 根据图表类型调整填充选项
            if (chartType === 'line') {
                overdueChart.data.datasets[0].fill = false;
            } else if (chartType === 'bar') {
                overdueChart.data.datasets[0].fill = true;
            }
            
            overdueChart.update('active');
            console.log(`✅ 逾期图表类型已切换为: ${chartType}`);
        } else {
            console.error(`❌ 图表未找到或未初始化: ${chartId}`);
        }
    } catch (error) {
        console.error(`❌ 切换图表类型失败: ${error.message}`);
    }
}

// 将数据转换为饼图格式
function convertToPieData(originalData, title) {
    if (!originalData || !originalData.datasets || originalData.datasets.length < 2) {
        return originalData;
    }
    
    // 计算总和
    const dataset1 = originalData.datasets[0];
    const dataset2 = originalData.datasets[1];
    const total1 = dataset1.data.reduce((sum, val) => sum + val, 0);
    const total2 = dataset2.data.reduce((sum, val) => sum + val, 0);
    
    return {
        labels: [dataset1.label, dataset2.label],
        datasets: [{
            label: title,
            data: [total1, total2],
            backgroundColor: [
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 99, 132, 0.8)'
            ],
            borderColor: [
                'rgba(54, 162, 235, 1)',
                'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 2
        }]
    };
}

// 获取饼图选项
function getPieChartOptions(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title
            },
            legend: {
                position: 'right'
            }
        }
    };
}

// 获取标准图表选项
function getStandardChartOptions(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title
            },
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };
}

// 窗口大小变化处理
function handleResize() {
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(function() {
        try {
            // 重新计算表格布局
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().columns.adjust().responsive.recalc();
                }
            });
            
            // 重新优化移动端显示
            optimizeForMobile();
            
            console.log('窗口大小变化处理完成');
        } catch(e) {
            console.error('窗口大小变化处理失败:', e);
        }
    }, 250);
}

// 导出图表功能
function exportCharts() {
    try {
        // 创建一个临时画布
        var tempCanvas = document.createElement('canvas');
        tempCanvas.width = 1200;
        tempCanvas.height = 800;
        
        // 将订单图表合并到临时画布
        if (orderChart) {
            var orderCanvas = document.getElementById('orderChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(orderCanvas, 0, 0, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 将逾期图表合并到临时画布
        if (overdueChart) {
            var overdueCanvas = document.getElementById('overdueChart');
            var ctx = tempCanvas.getContext('2d');
            ctx.drawImage(overdueCanvas, 0, tempCanvas.height / 2, tempCanvas.width, tempCanvas.height / 2);
        }
        
        // 导出为图片
        var link = document.createElement('a');
        link.download = '数据汇总图表_' + new Date().toISOString().slice(0, 10) + '.png';
        link.href = tempCanvas.toDataURL('image/png');
        link.click();
    } catch (err) {
        console.error('导出图表失败:', err);
        alert('导出图表失败，请重试');
    }
}

// 客户搜索函数
function searchCustomer() {
    var customerName = document.getElementById('customerName').value.trim();
    if (!customerName) {
        alert('请输入客户姓名');
        return;
    }
    
    window.location.href = "/?customerName=" + encodeURIComponent(customerName) + "&tab=customer";
}

// 检测API状态
function checkApiStatus() {
    var apiStatusElement = document.getElementById('apiStatus');
    if (!apiStatusElement) return;
    
    // 设置超时时间（5秒）
    const timeout = 5000;
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    fetch("{{ url_for('api.ping') }}", {
        signal: controller.signal
    })
    .then(function(response) {
        clearTimeout(timeoutId);
        if (!response.ok) {
            throw new Error('API响应状态: ' + response.status);
        }
        return response.json();
    })
    .then(function(data) {
        if (data.status === 'ok') {
            apiStatusElement.innerHTML = '<span class="badge bg-success">正常</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-warning">异常</span>';
        }
    })
    .catch(function(error) {
        clearTimeout(timeoutId);
        console.error('API状态检查失败:', error);
        if (error.name === 'AbortError') {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">超时</span>';
        } else {
            apiStatusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
        }
        
        // 5秒后重试
        setTimeout(checkApiStatus, 5000);
    });
}
</script>
{% endblock %}