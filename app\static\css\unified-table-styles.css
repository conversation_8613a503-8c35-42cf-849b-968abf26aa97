/**
 * 统一表格样式
 * 确保日期筛选、客户搜索和逾期订单页面的表格样式完全一致
 */

/* 基础表格样式 - 应用到所有数据表格 */
.data-table {
    width: 100% !important;
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
}

/* 表格容器样式 */
.dataTables_wrapper {
    width: 100% !important;
    margin: 0 auto;
}

/* 表头和内容单元格基础样式 */
.dataTable > thead > tr > th,
.dataTable > tbody > tr > td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle !important;
    padding: 8px 12px;
}

/* 表头样式统一 */
.data-table thead th {
    background-color: #f8f9fa !important;
    color: #495057;
    font-weight: 600;
    text-align: center !important;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
}

/* 表格内容单元格样式 */
.data-table tbody td {
    padding: 10px;
    text-align: center !important;
    vertical-align: middle;
    font-size: 14px;
    font-family: "Microsoft YaHei", sans-serif;
}

/* 响应式控制列样式 */
.data-table td.dtr-control,
.data-table th.dtr-control {
    position: relative;
    text-align: center !important;
    cursor: pointer;
    padding-left: 0;
    padding-right: 0;
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    vertical-align: middle;
}

/* 展开/折叠按钮样式 */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    content: '+';
    background-color: #007bff;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0.25rem rgba(0, 123, 255, 0.5);
    position: absolute;
    line-height: 1;
    font-size: 16px;
}

/* 展开状态按钮样式 */
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
    content: '-';
    background-color: #dc3545;
}

/* 金额字段样式 */
.amount-field {
    font-weight: bold;
    text-align: right !important;
    color: #007bff;
    font-family: Consolas, monospace;
    font-weight: 500;
}

/* 分类字段标签样式 */
.category-field .badge {
    font-size: 0.85em;
    font-weight: normal;
}

/* 产品类型字段样式 - 区分不同产品类型 */
.category-field .badge[data-product-type="电商"] {
    background-color: #cfe2ff !important;
    color: #084298 !important;
    border: 1px solid #b6d4fe;
}

.category-field .badge[data-product-type="租赁"] {
    background-color: #d1e7dd !important;
    color: #0f5132 !important;
    border: 1px solid #badbcc;
}

/* 日期字段标签样式 */
.date-field .badge {
    font-size: 0.85em;
    font-weight: normal;
}

/* 状态字段标签样式 */
.status-field .badge {
    font-size: 0.9em;
}

/* 奇偶行颜色区分 */
.data-table tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02);
}

.data-table tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}

/* 状态行样式 - 与逾期订单保持一致 */
tr[data-status="逾期未还"], 
tr[data-status="严重逾期"] {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

tr[data-status="逾期还款"], 
tr[data-status="轻微逾期"], 
tr[data-status="催收中"] {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

tr[data-status="按时还款"], 
tr[data-status="已结清"], 
tr[data-status="正常"] {
    background-color: rgba(25, 135, 84, 0.05) !important;
}

tr[data-status="提前还款"] {
    background-color: rgba(13, 110, 253, 0.05) !important;
}

tr[data-status="账单日"] {
    background-color: rgba(255, 193, 7, 0.05) !important;
}

/* 状态单元格样式 */
.data-table td.逾期还款,
.data-table td.逾期未还,
.data-table td.催收,
.data-table td.诉讼 {
    font-weight: 600;
    background-color: rgba(255, 199, 206, 0.4);
    color: #d63031;
}

.data-table td.提前还款 {
    font-weight: 500;
    background-color: rgba(209, 216, 224, 0.3);
    color: #0984e3;
}

.data-table td.按时还款 {
    font-weight: 500;
    background-color: rgba(200, 247, 197, 0.3);
    color: #00b894;
}

.data-table td.账单日 {
    font-weight: 500;
    background-color: rgba(255, 234, 167, 0.3);
    color: #f39c12;
}

/* 徽章样式统一 */
.data-table .badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.85em;
    font-weight: 500;
    border-radius: 0.25rem;
    white-space: nowrap;
}

/* 状态徽章颜色 */
.badge.bg-warning.text-dark {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #fff !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #fff !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #fff !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
}

.badge.bg-info.text-dark {
    background-color: #0dcaf0 !important;
    color: #212529 !important;
}

/* 长文本内容样式 */
.expandable-content {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s ease;
}

.expandable-content.expanded {
    white-space: normal;
    max-width: none;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 10;
    position: relative;
}

/* 特定字段宽度设置 - 与逾期订单保持一致 */
.data-table th:nth-child(2),  /* 订单编号 */
.data-table td:nth-child(2) {
    min-width: 130px;
}

.data-table th:nth-child(3),  /* 客户姓名 */
.data-table td:nth-child(3) {
    min-width: 100px;
}

.data-table th:nth-child(4),  /* 状态相关 */
.data-table td:nth-child(4) {
    min-width: 100px;
}

.data-table th:nth-child(5),  /* 客户手机 */
.data-table td:nth-child(5) {
    min-width: 120px;
}

/* 移动设备优化 */
@media (max-width: 768px) {
    .data-table th,
    .data-table td {
        min-width: 60px !important;
        padding: 8px 6px;
        font-size: 0.8rem;
    }
    
    .data-table td.dtr-control,
    .data-table th.dtr-control {
        min-width: 40px !important;
        width: 40px !important;
    }
    
    /* 移动设备下的折叠按钮样式优化 */
    table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
        width: 18px;
        height: 18px;
        font-size: 14px;
    }
    
    .badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
    }
    
    .expandable-content {
        max-width: 150px;
    }
}

/* 确保表格容器不会造成水平滚动 */
.table-responsive {
    overflow-x: auto !important;
    max-width: 100% !important;
}

/* 防止DataTables重新计算宽度时覆盖样式 */
.data-table th {
    box-sizing: border-box !important;
    position: relative !important;
}

/* 排序图标位置修复 */
table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    bottom: 0.5em;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    top: 0.5em;
}

/* 确保所有标签页的表格样式一致 */
#filter .data-table,
#customer .data-table,
#overdue .data-table {
    width: 100% !important;
    margin: 0 !important;
}

/* 确保表格行的hover效果一致 */
#filter .data-table tbody tr:hover,
#customer .data-table tbody tr:hover,
#overdue .data-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* 分页控件样式 */
.pagination-container {
    margin-top: 20px;
    margin-bottom: 30px;
}

/* 表格控件样式优化 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    margin-left: 0.5rem;
    max-width: 200px;
}

/* 确保按钮在各种状态下都正确居中 */
.table-responsive .data-table td.dtr-control::before {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
} 