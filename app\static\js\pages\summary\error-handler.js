/**
 * 企业级错误处理模块
 * 提供统一的错误处理、日志记录和用户反馈机制
 */

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 100; // 最大错误记录数
        this.isProduction = this.detectEnvironment();
        this.errorQueue = [];
        this.isReporting = false;
        
        // 错误类型定义
        this.ERROR_TYPES = {
            NETWORK: 'network',
            VALIDATION: 'validation',
            RUNTIME: 'runtime',
            API: 'api',
            CHART: 'chart',
            TABLE: 'table',
            UI: 'ui'
        };
        
        // 错误级别定义
        this.ERROR_LEVELS = {
            LOW: 'low',
            MEDIUM: 'medium',
            HIGH: 'high',
            CRITICAL: 'critical'
        };
        
        // 绑定方法上下文
        this.handleError = this.handleError.bind(this);
        this.handleGlobalError = this.handleGlobalError.bind(this);
        this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
    }

    /**
     * 初始化错误处理器
     */
    initialize() {
        console.log('ErrorHandler: 错误处理器已初始化');
        
        // 设置全局错误监听
        this.setupGlobalErrorHandlers();
        
        // 创建错误显示容器
        this.createErrorContainer();
        
        // 开始错误报告队列处理
        this.startErrorReporting();
    }

    /**
     * 检测运行环境
     * @returns {boolean} 是否为生产环境
     */
    detectEnvironment() {
        return window.location.hostname !== 'localhost' && 
               window.location.hostname !== '127.0.0.1' &&
               !window.location.hostname.includes('dev');
    }

    /**
     * 设置全局错误处理器
     */
    setupGlobalErrorHandlers() {
        // JavaScript错误处理
        window.addEventListener('error', this.handleGlobalError);
        
        // Promise rejection处理
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        
        // 覆盖console.error
        this.interceptConsoleError();
    }

    /**
     * 拦截console.error
     */
    interceptConsoleError() {
        const originalError = console.error;
        console.error = (...args) => {
            // 记录错误但不显示给用户
            this.logError('Console Error', args.join(' '), { 
                type: this.ERROR_TYPES.RUNTIME,
                level: this.ERROR_LEVELS.LOW 
            });
            
            // 在开发环境下仍然显示原始错误
            if (!this.isProduction) {
                originalError.apply(console, args);
            }
        };
    }

    /**
     * 处理全局JavaScript错误
     * @param {ErrorEvent} event - 错误事件
     */
    handleGlobalError(event) {
        const error = {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        };
        
        this.handleError('Global JavaScript Error', error, {
            type: this.ERROR_TYPES.RUNTIME,
            level: this.ERROR_LEVELS.HIGH
        });
    }

    /**
     * 处理未处理的Promise rejection
     * @param {PromiseRejectionEvent} event - Promise rejection事件
     */
    handleUnhandledRejection(event) {
        this.handleError('Unhandled Promise Rejection', event.reason, {
            type: this.ERROR_TYPES.RUNTIME,
            level: this.ERROR_LEVELS.MEDIUM
        });
    }

    /**
     * 处理错误的主要方法
     * @param {string} context - 错误上下文
     * @param {Error|string|Object} error - 错误对象
     * @param {Object} options - 选项
     */
    handleError(context, error, options = {}) {
        const errorInfo = this.processError(context, error, options);
        
        // 记录错误
        this.logError(context, error, options);
        
        // 根据错误级别决定用户体验
        this.handleUserExperience(errorInfo);
        
        // 添加到报告队列
        this.queueErrorReport(errorInfo);
    }

    /**
     * 处理用户体验
     * @param {Object} errorInfo - 错误信息
     */
    handleUserExperience(errorInfo) {
        const { level, type, userMessage } = errorInfo;
        
        // 高级别错误显示给用户
        if (level === this.ERROR_LEVELS.HIGH || level === this.ERROR_LEVELS.CRITICAL) {
            this.showUserError(userMessage || '系统遇到问题，请稍后重试', type);
        }
        
        // 关键错误可能需要页面重载
        if (level === this.ERROR_LEVELS.CRITICAL) {
            setTimeout(() => {
                if (confirm('系统遇到严重问题，是否重新加载页面？')) {
                    window.location.reload();
                }
            }, 3000);
        }
    }

    /**
     * 处理错误信息
     * @param {string} context - 错误上下文
     * @param {Error|string|Object} error - 错误对象
     * @param {Object} options - 选项
     * @returns {Object} 处理后的错误信息
     */
    processError(context, error, options) {
        const timestamp = new Date().toISOString();
        const userAgent = navigator.userAgent;
        const url = window.location.href;
        
        let errorDetails = {};
        
        if (error instanceof Error) {
            errorDetails = {
                name: error.name,
                message: error.message,
                stack: error.stack
            };
        } else if (typeof error === 'string') {
            errorDetails = { message: error };
        } else if (typeof error === 'object') {
            errorDetails = { ...error };
        }
        
        const errorInfo = {
            id: this.generateErrorId(),
            timestamp,
            context,
            error: errorDetails,
            type: options.type || this.ERROR_TYPES.RUNTIME,
            level: options.level || this.ERROR_LEVELS.MEDIUM,
            userMessage: options.userMessage,
            metadata: {
                userAgent,
                url,
                ...options.metadata
            }
        };
        
        return errorInfo;
    }

    /**
     * 记录错误
     * @param {string} context - 错误上下文
     * @param {Error|string|Object} error - 错误对象
     * @param {Object} options - 选项
     */
    logError(context, error, options) {
        const errorInfo = this.processError(context, error, options);
        
        // 添加到错误列表
        this.errors.unshift(errorInfo);
        
        // 限制错误记录数量
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(0, this.maxErrors);
        }
        
        // 在开发环境下输出详细错误信息
        if (!this.isProduction) {
            console.group(`🚨 ${context}`);
            console.error('Error:', error);
            console.log('Error Info:', errorInfo);
            console.groupEnd();
        }
    }

    /**
     * 显示用户错误
     * @param {string} message - 错误消息
     * @param {string} type - 错误类型
     */
    showUserError(message, type = 'error') {
        const alertClass = this.getAlertClass(type);
        const iconClass = this.getIconClass(type);
        
        const errorElement = document.createElement('div');
        errorElement.className = `alert ${alertClass} alert-dismissible fade show position-fixed error-toast`;
        errorElement.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        errorElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${iconClass} me-2"></i>
                <div class="flex-grow-1">
                    <strong>提示</strong><br>
                    <small>${message}</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(errorElement);
        
        // 自动移除
        setTimeout(() => {
            if (errorElement.parentNode) {
                errorElement.remove();
            }
        }, 6000);
    }

    /**
     * 获取警告框样式类
     * @param {string} type - 错误类型
     * @returns {string} CSS类名
     */
    getAlertClass(type) {
        const classMap = {
            [this.ERROR_TYPES.NETWORK]: 'alert-warning',
            [this.ERROR_TYPES.VALIDATION]: 'alert-info',
            [this.ERROR_TYPES.API]: 'alert-danger',
            [this.ERROR_TYPES.CHART]: 'alert-warning',
            [this.ERROR_TYPES.TABLE]: 'alert-warning',
            [this.ERROR_TYPES.UI]: 'alert-secondary'
        };
        
        return classMap[type] || 'alert-danger';
    }

    /**
     * 获取图标样式类
     * @param {string} type - 错误类型
     * @returns {string} 图标类名
     */
    getIconClass(type) {
        const iconMap = {
            [this.ERROR_TYPES.NETWORK]: 'bi bi-wifi-off',
            [this.ERROR_TYPES.VALIDATION]: 'bi bi-info-circle',
            [this.ERROR_TYPES.API]: 'bi bi-exclamation-triangle',
            [this.ERROR_TYPES.CHART]: 'bi bi-bar-chart',
            [this.ERROR_TYPES.TABLE]: 'bi bi-table',
            [this.ERROR_TYPES.UI]: 'bi bi-gear'
        };
        
        return iconMap[type] || 'bi bi-exclamation-triangle-fill';
    }

    /**
     * 创建错误显示容器
     */
    createErrorContainer() {
        if (!document.getElementById('errorContainer')) {
            const container = document.createElement('div');
            container.id = 'errorContainer';
            container.style.cssText = `
                position: fixed;
                top: 0;
                right: 0;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    /**
     * 生成错误ID
     * @returns {string} 错误ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 添加错误到报告队列
     * @param {Object} errorInfo - 错误信息
     */
    queueErrorReport(errorInfo) {
        this.errorQueue.push(errorInfo);
    }

    /**
     * 开始错误报告
     */
    startErrorReporting() {
        // 每5秒处理一次错误队列
        setInterval(() => {
            if (this.errorQueue.length > 0 && !this.isReporting) {
                this.reportErrors();
            }
        }, 5000);
    }

    /**
     * 报告错误
     */
    async reportErrors() {
        if (this.errorQueue.length === 0) return;
        
        this.isReporting = true;
        const errorsToReport = [...this.errorQueue];
        this.errorQueue = [];
        
        try {
            // 这里可以发送到错误监控服务
            await this.sendErrorReport(errorsToReport);
        } catch (reportError) {
            console.warn('Failed to report errors:', reportError);
            // 将失败的错误重新加入队列
            this.errorQueue.unshift(...errorsToReport);
        } finally {
            this.isReporting = false;
        }
    }

    /**
     * 发送错误报告
     * @param {Array} errors - 错误列表
     */
    async sendErrorReport(errors) {
        // 在生产环境中，这里应该发送到实际的错误监控服务
        if (this.isProduction) {
            // 示例：发送到错误监控API
            // await fetch('/api/errors', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ errors })
            // });
        } else {
            console.log('Error Report (Development):', errors);
        }
    }

    /**
     * 获取错误统计
     * @returns {Object} 错误统计信息
     */
    getErrorStats() {
        const stats = {
            total: this.errors.length,
            byType: {},
            byLevel: {},
            recent: this.errors.slice(0, 10)
        };
        
        this.errors.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1;
        });
        
        return stats;
    }

    /**
     * 清除错误记录
     */
    clearErrors() {
        this.errors = [];
        this.errorQueue = [];
        console.log('ErrorHandler: 错误记录已清除');
    }

    /**
     * 手动报告错误
     * @param {string} context - 错误上下文
     * @param {string} message - 错误消息
     * @param {Object} options - 选项
     */
    reportError(context, message, options = {}) {
        this.handleError(context, new Error(message), {
            type: this.ERROR_TYPES.RUNTIME,
            level: this.ERROR_LEVELS.MEDIUM,
            ...options
        });
    }

    /**
     * 包装函数以捕获错误
     * @param {Function} func - 要包装的函数
     * @param {string} context - 上下文
     * @returns {Function} 包装后的函数
     */
    wrapFunction(func, context) {
        return (...args) => {
            try {
                const result = func.apply(this, args);
                
                // 如果返回Promise，则捕获rejection
                if (result instanceof Promise) {
                    return result.catch(error => {
                        this.handleError(context, error, {
                            type: this.ERROR_TYPES.RUNTIME,
                            level: this.ERROR_LEVELS.MEDIUM
                        });
                        throw error; // 重新抛出错误
                    });
                }
                
                return result;
            } catch (error) {
                this.handleError(context, error, {
                    type: this.ERROR_TYPES.RUNTIME,
                    level: this.ERROR_LEVELS.MEDIUM
                });
                throw error; // 重新抛出错误
            }
        };
    }

    /**
     * 销毁错误处理器
     */
    destroy() {
        window.removeEventListener('error', this.handleGlobalError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
        
        this.clearErrors();
        console.log('ErrorHandler: 错误处理器已销毁');
    }
}

// 创建全局错误处理器实例
window.ErrorHandler = ErrorHandler;

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    if (!window.globalErrorHandler) {
        window.globalErrorHandler = new ErrorHandler();
        window.globalErrorHandler.initialize();
    }
});