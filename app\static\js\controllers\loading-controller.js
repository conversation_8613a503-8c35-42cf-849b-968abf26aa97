/**
 * 加载状态控制器
 * 统一管理加载状态的显示和隐藏
 */
const LoadingController = {
    // 配置
    config: {
        defaultMessage: '加载中...',
        fadeInDuration: 200,
        fadeOutDuration: 300,
        minDisplayTime: 300,
        overlayOpacity: 0.7
    },
    
    // 状态
    state: {
        isLoading: false,
        loadingStartTime: 0,
        activeRequests: 0,
        overlayElement: null,
        spinnerElement: null,
        messageElement: null
    },
    
    // 初始化
    init: function() {
        console.log('初始化加载状态控制器');
        this.createElements();
        this.setupAjaxInterceptor();
    },
    
    // 创建加载指示器元素
    createElements: function() {
        // 检查是否已经存在加载遮罩
        const existingOverlay = document.getElementById('loadingOverlay');
        if (existingOverlay) {
            // 使用现有的加载遮罩
            this.state.overlayElement = existingOverlay;
            this.state.spinnerElement = existingOverlay.querySelector('.spinner-border');
            this.state.messageElement = existingOverlay.querySelector('.loading-message');
            return;
        }
        
        // 创建新的加载遮罩
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay';
        overlay.style.display = 'none';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = `rgba(0, 0, 0, ${this.config.overlayOpacity})`;
        overlay.style.zIndex = '9999';
        overlay.style.display = 'flex';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        overlay.style.flexDirection = 'column';
        overlay.style.opacity = '0';
        overlay.style.transition = `opacity ${this.config.fadeInDuration}ms ease`;
        
        // 创建加载中转圈图标
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border text-light';
        spinner.setAttribute('role', 'status');
        spinner.style.width = '3rem';
        spinner.style.height = '3rem';
        
        // 创建加载中文字提示
        const message = document.createElement('div');
        message.className = 'loading-message text-light mt-3';
        message.textContent = this.config.defaultMessage;
        
        // 组装元素
        overlay.appendChild(spinner);
        overlay.appendChild(message);
        document.body.appendChild(overlay);
        
        // 保存元素引用
        this.state.overlayElement = overlay;
        this.state.spinnerElement = spinner;
        this.state.messageElement = message;
    },
    
    // 设置AJAX请求拦截器，自动显示/隐藏加载状态
    setupAjaxInterceptor: function() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            // 显示加载状态
            this.incrementRequests();
            
            try {
                // 执行原始fetch请求
                const response = await originalFetch(...args);
                return response;
            } catch (error) {
                throw error;
            } finally {
                // 隐藏加载状态
                this.decrementRequests();
            }
        };
        
        // 拦截XMLHttpRequest请求
        const originalXhrOpen = XMLHttpRequest.prototype.open;
        const originalXhrSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(...args) {
            // 保存原始引用
            this._loadingController = LoadingController;
            return originalXhrOpen.apply(this, args);
        };
        
        XMLHttpRequest.prototype.send = function(...args) {
            // 显示加载状态
            this._loadingController.incrementRequests();
            
            // 添加事件监听器
            this.addEventListener('loadend', () => {
                // 隐藏加载状态
                this._loadingController.decrementRequests();
            });
            
            return originalXhrSend.apply(this, args);
        };
    },
    
    // 递增请求计数
    incrementRequests: function() {
        this.state.activeRequests++;
        if (this.state.activeRequests === 1) {
            this.show();
        }
    },
    
    // 递减请求计数
    decrementRequests: function() {
        this.state.activeRequests--;
        if (this.state.activeRequests <= 0) {
            this.state.activeRequests = 0;
            this.hide();
        }
    },
    
    // 显示加载状态
    show: function(message) {
        // 保存加载开始时间
        this.state.loadingStartTime = Date.now();
        this.state.isLoading = true;
        
        // 设置提示消息
        if (message && this.state.messageElement) {
            this.state.messageElement.textContent = message;
        } else if (this.state.messageElement) {
            this.state.messageElement.textContent = this.config.defaultMessage;
        }
        
        // 显示加载遮罩
        if (this.state.overlayElement) {
            this.state.overlayElement.style.display = 'flex';
            
            // 使用requestAnimationFrame确保显示过渡效果
            requestAnimationFrame(() => {
                this.state.overlayElement.style.opacity = '1';
            });
        }
    },
    
    // 隐藏加载状态
    hide: function() {
        // 如果没有活动的加载状态，直接返回
        if (!this.state.isLoading) {
            return;
        }
        
        // 计算已经显示的时间
        const displayTime = Date.now() - this.state.loadingStartTime;
        
        // 确保至少显示最小时间，避免闪烁
        const delay = Math.max(0, this.config.minDisplayTime - displayTime);
        
        setTimeout(() => {
            this.state.isLoading = false;
            
            // 隐藏加载遮罩
            if (this.state.overlayElement) {
                this.state.overlayElement.style.opacity = '0';
                
                // 等待过渡效果完成后隐藏元素
                setTimeout(() => {
                    if (!this.state.isLoading) {
                        this.state.overlayElement.style.display = 'none';
                    }
                }, this.config.fadeOutDuration);
            }
        }, delay);
    },
    
    // 显示错误消息
    showError: function(message, duration = 3000) {
        // 如果当前有加载状态，先隐藏
        this.hide();
        
        // 创建错误提示框
        const errorBox = document.createElement('div');
        errorBox.className = 'alert alert-danger fixed-top w-75 mx-auto mt-3';
        errorBox.style.zIndex = '10000';
        errorBox.style.opacity = '0';
        errorBox.style.transition = 'opacity 0.3s ease';
        errorBox.innerHTML = `
            <button type="button" class="btn-close float-end" onclick="this.parentNode.remove()"></button>
            <strong>错误：</strong> ${message}
        `;
        
        // 添加到页面
        document.body.appendChild(errorBox);
        
        // 显示错误提示框
        requestAnimationFrame(() => {
            errorBox.style.opacity = '1';
        });
        
        // 自动关闭
        setTimeout(() => {
            errorBox.style.opacity = '0';
            setTimeout(() => {
                if (errorBox.parentNode) {
                    errorBox.parentNode.removeChild(errorBox);
                }
            }, 300);
        }, duration);
    }
};

// 当DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    LoadingController.init();
}); 