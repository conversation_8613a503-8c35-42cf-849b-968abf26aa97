/**
 * 汇总页面控制器
 * 负责协调所有模块，提供与原始页面完全一致的功能
 */

class SummaryPageController {
    constructor() {
        // 模块实例
        this.chartManager = null;
        this.tableManager = null;
        this.apiClient = null;
        this.loadingManager = null;
        this.errorHandler = null;
        this.performanceOptimizer = null;
        
        // 页面数据
        this.orderChartData = {};
        this.overdueChartData = {};
        
        // 状态管理
        this.isInitialized = false;
        this.resizeTimer = null;
        
        // 绑定方法上下文
        this.initialize = this.initialize.bind(this);
        this.handleResize = this.handleResize.bind(this);
        this.exportCharts = this.exportCharts.bind(this);
        this.searchCustomer = this.searchCustomer.bind(this);
        this.optimizeForMobile = this.optimizeForMobile.bind(this);
    }

    /**
     * 初始化页面控制器
     */
    async initialize() {
        console.log('SummaryPageController: 开始初始化汇总页面');
        
        try {
            // 解析页面数据
            this.parsePageData();
            
            // 初始化所有模块
            await this.initializeModules();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化下拉菜单功能
            this.initializeDropdowns();
            
            // 隐藏初始加载状态
            this.loadingManager.hideGlobalLoading();
            
            this.isInitialized = true;
            console.log('SummaryPageController: 页面初始化完成');
            
        } catch (error) {
            console.error('SummaryPageController: 初始化失败:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * 解析页面数据
     */
    parsePageData() {
        console.log('SummaryPageController: 解析页面数据');
        
        // 从全局变量获取图表数据（保持与原始代码一致）
        if (window.orderChartData) {
            try {
                this.orderChartData = typeof window.orderChartData === 'string' 
                    ? JSON.parse(window.orderChartData) 
                    : window.orderChartData;
            } catch (e) {
                console.error('SummaryPageController: 解析订单图表数据失败:', e);
            }
        }

        if (window.overdueChartData) {
            try {
                this.overdueChartData = typeof window.overdueChartData === 'string' 
                    ? JSON.parse(window.overdueChartData) 
                    : window.overdueChartData;
            } catch (e) {
                console.error('SummaryPageController: 解析逾期图表数据失败:', e);
            }
        }
    }

    /**
     * 初始化所有模块
     */
    async initializeModules() {
        console.log('SummaryPageController: 初始化模块');
        
        try {
            // 首先初始化错误处理器
            this.errorHandler = window.globalErrorHandler || new ErrorHandler();
            if (!window.globalErrorHandler) {
                this.errorHandler.initialize();
                window.globalErrorHandler = this.errorHandler;
            }
            
            // 初始化性能优化器
            this.performanceOptimizer = new PerformanceOptimizer();
            this.performanceOptimizer.initialize({
                enablePerformanceMonitoring: true,
                enableLazyLoading: true,
                enableMemoryOptimization: true
            });
            
            // 初始化加载管理器
            this.loadingManager = new LoadingManager();
            this.loadingManager.initialize();
            
            // 初始化API客户端
            this.apiClient = new ApiClient();
            this.apiClient.initialize({
                timeout: 10000,
                retryCount: 3
            });
            
            // 初始化表格管理器（包装错误处理）
            this.tableManager = new TableManager();
            await this.performanceOptimizer.measurePerformance('table-init', async () => {
                this.tableManager.initialize();
            });
            
            // 初始化图表管理器（包装错误处理）
            this.chartManager = new ChartManager();
            await this.performanceOptimizer.measurePerformance('chart-init', async () => {
                this.chartManager.initialize(this.orderChartData, this.overdueChartData);
            });
            
            // 等待所有模块初始化完成
            await this.waitForModulesReady();
            
        } catch (error) {
            if (this.errorHandler) {
                this.errorHandler.handleError('模块初始化', error, {
                    type: this.errorHandler.ERROR_TYPES.RUNTIME,
                    level: this.errorHandler.ERROR_LEVELS.CRITICAL,
                    userMessage: '页面模块初始化失败，请刷新页面重试'
                });
            }
            throw error;
        }
    }

    /**
     * 等待模块准备就绪
     */
    async waitForModulesReady() {
        const maxWait = 5000; // 最大等待5秒
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWait) {
            if (this.tableManager.isInitialized) {
                break;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        console.log('SummaryPageController: 所有模块已准备就绪');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        console.log('SummaryPageController: 设置事件监听器');
        
        // 窗口大小变化事件
        window.addEventListener('resize', this.handleResize);
        
        // 导出图表按钮事件
        const exportButton = document.querySelector('button[onclick="exportCharts()"]');
        if (exportButton) {
            exportButton.removeAttribute('onclick');
            exportButton.addEventListener('click', this.exportCharts);
        }
        
        // 客户搜索功能（如果存在）
        const searchButton = document.querySelector('button[onclick="searchCustomer()"]');
        if (searchButton) {
            searchButton.removeAttribute('onclick');
            searchButton.addEventListener('click', this.searchCustomer);
        }
        
        // 表单提交事件
        const dateForm = document.querySelector('form[onsubmit="showLoading()"]');
        if (dateForm) {
            dateForm.removeAttribute('onsubmit');
            dateForm.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    }

    /**
     * 初始化下拉菜单功能（保持与原始代码完全一致）
     */
    initializeDropdowns() {
        console.log('SummaryPageController: 初始化下拉菜单');
        
        try {
            // 手动为指定的下拉菜单添加点击事件
            const dataViewSelector = document.getElementById('dataViewSelector');
            if (dataViewSelector) {
                dataViewSelector.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const dropdown = document.querySelector('.dropdown-menu.view-selector');
                    if (dropdown) {
                        dropdown.classList.toggle('show');
                        dropdown.style.display = dropdown.classList.contains('show') ? 'block' : 'none';
                        dropdown.style.position = 'absolute';
                        dropdown.style.transform = 'translate3d(0px, 38px, 0px)';
                        dropdown.style.top = '0px';
                        dropdown.style.left = '0px';
                        dropdown.style.willChange = 'transform';
                    }
                });
                
                // 点击其他区域时关闭下拉菜单
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.dropdown')) {
                        const dropdown = document.querySelector('.dropdown-menu.view-selector');
                        if (dropdown && dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                            dropdown.style.display = 'none';
                        }
                    }
                });
                
                // 处理菜单项点击
                const menuItems = document.querySelectorAll('.dropdown-menu.view-selector .dropdown-item');
                menuItems.forEach((item) => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        // 移除所有active类
                        menuItems.forEach((mi) => {
                            mi.classList.remove('active');
                        });
                        
                        // 添加active类到当前项
                        item.classList.add('active');
                        
                        // 调用视图切换函数
                        if (typeof switchDataView === 'function') {
                            switchDataView(item.getAttribute('data-view'));
                        }
                        
                        // 关闭下拉菜单
                        const dropdown = document.querySelector('.dropdown-menu.view-selector');
                        if (dropdown) {
                            dropdown.classList.remove('show');
                            dropdown.style.display = 'none';
                        }
                    });
                });
            }
        } catch (e) {
            console.error('SummaryPageController: 手动初始化下拉菜单失败:', e);
        }
        
        // 添加CSS样式确保下拉菜单正常显示
        this.addDropdownStyles();
    }

    /**
     * 添加下拉菜单样式
     */
    addDropdownStyles() {
        const existingStyle = document.getElementById('dropdown-styles');
        if (!existingStyle) {
            const style = document.createElement('style');
            style.id = 'dropdown-styles';
            style.textContent = `
                .dropdown-menu.show {
                    display: block !important;
                    position: absolute !important;
                    z-index: 1000 !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 处理表单提交
     * @param {Event} event - 表单提交事件
     */
    handleFormSubmit(event) {
        this.loadingManager.showGlobalLoading('正在查询数据...');
    }

    /**
     * 窗口大小变化处理
     */
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            try {
                // 通知图表管理器处理大小变化
                if (this.chartManager) {
                    this.chartManager.handleResize();
                }
                
                // 通知表格管理器处理大小变化
                if (this.tableManager) {
                    this.tableManager.handleResize();
                }
                
                // 优化移动端显示
                this.optimizeForMobile();
                
                console.log('SummaryPageController: 窗口大小变化处理完成');
            } catch (e) {
                console.error('SummaryPageController: 窗口大小变化处理失败:', e);
            }
        }, 250);
    }

    /**
     * 移动端优化
     */
    optimizeForMobile() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 移动端优化逻辑
            document.querySelectorAll('.card-body').forEach(cardBody => {
                if (!cardBody.classList.contains('mobile-optimized')) {
                    cardBody.style.padding = '0.75rem';
                    cardBody.classList.add('mobile-optimized');
                }
            });
            
            // 优化图表容器
            document.querySelectorAll('.chart-container').forEach(container => {
                container.style.height = '250px';
            });
        } else {
            // 桌面端恢复
            document.querySelectorAll('.card-body.mobile-optimized').forEach(cardBody => {
                cardBody.style.padding = '';
                cardBody.classList.remove('mobile-optimized');
            });
            
            document.querySelectorAll('.chart-container').forEach(container => {
                container.style.height = '300px';
            });
        }
    }

    /**
     * 导出图表功能
     */
    exportCharts() {
        if (this.chartManager) {
            this.chartManager.exportCharts();
        } else {
            console.error('SummaryPageController: 图表管理器未初始化');
        }
    }

    /**
     * 客户搜索功能
     */
    searchCustomer() {
        const customerName = document.getElementById('customerName')?.value?.trim();
        if (!customerName) {
            alert('请输入客户姓名');
            return;
        }
        
        window.location.href = `/?customerName=${encodeURIComponent(customerName)}&tab=customer`;
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError(error) {
        console.error('SummaryPageController: 初始化错误:', error);
        
        // 隐藏加载状态
        if (this.loadingManager) {
            this.loadingManager.hideGlobalLoading();
        }
        
        // 显示错误消息
        const errorMessage = document.createElement('div');
        errorMessage.className = 'alert alert-danger';
        errorMessage.innerHTML = `
            <h5>页面初始化失败</h5>
            <p>请刷新页面重试，如果问题持续存在，请联系管理员。</p>
            <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
        `;
        
        const container = document.querySelector('.main-content');
        if (container) {
            container.insertBefore(errorMessage, container.firstChild);
        }
    }

    /**
     * 获取页面状态
     * @returns {Object} 页面状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            modules: {
                chartManager: !!this.chartManager,
                tableManager: this.tableManager?.getStatus(),
                apiClient: !!this.apiClient,
                loadingManager: !!this.loadingManager,
                errorHandler: !!this.errorHandler,
                performanceOptimizer: !!this.performanceOptimizer
            },
            hasOrderData: !!(this.orderChartData && this.orderChartData.labels),
            hasOverdueData: !!(this.overdueChartData && this.overdueChartData.labels),
            performanceReport: this.performanceOptimizer?.getPerformanceReport(),
            errorStats: this.errorHandler?.getErrorStats()
        };
    }

    /**
     * 刷新页面数据
     */
    async refreshData() {
        try {
            this.loadingManager.showGlobalLoading('正在刷新数据...');
            
            // 刷新表格数据
            if (this.tableManager) {
                this.tableManager.refreshTables();
            }
            
            // 重新初始化图表（如果有新数据）
            this.parsePageData();
            if (this.chartManager) {
                this.chartManager.destroy();
                this.chartManager.initialize(this.orderChartData, this.overdueChartData);
            }
            
            console.log('SummaryPageController: 数据刷新完成');
        } catch (error) {
            console.error('SummaryPageController: 数据刷新失败:', error);
        } finally {
            this.loadingManager.hideGlobalLoading();
        }
    }

    /**
     * 销毁页面控制器
     */
    destroy() {
        console.log('SummaryPageController: 开始销毁页面控制器');
        
        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);
        
        // 销毁所有模块（按依赖关系逆序销毁）
        if (this.chartManager) {
            this.chartManager.destroy();
        }
        
        if (this.tableManager) {
            this.tableManager.destroy();
        }
        
        if (this.apiClient) {
            this.apiClient.destroy();
        }
        
        if (this.loadingManager) {
            this.loadingManager.destroy();
        }
        
        if (this.performanceOptimizer) {
            this.performanceOptimizer.destroy();
        }
        
        // 注意：不销毁全局错误处理器，因为它可能被其他组件使用
        // if (this.errorHandler && this.errorHandler !== window.globalErrorHandler) {
        //     this.errorHandler.destroy();
        // }
        
        // 清理状态
        this.isInitialized = false;
        clearTimeout(this.resizeTimer);
        
        console.log('SummaryPageController: 页面控制器已销毁');
    }
}

// 创建全局实例
window.SummaryPageController = SummaryPageController;

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('SummaryPageController: DOM加载完成，开始初始化');
    
    // 创建页面控制器实例
    window.summaryPageManager = new SummaryPageController();
    
    // 延迟初始化以确保所有依赖加载完成
    setTimeout(() => {
        window.summaryPageManager.initialize();
    }, 100);
});

// 保持与原始代码的兼容性
window.initCharts = function() {
    if (window.summaryPageManager && window.summaryPageManager.chartManager) {
        window.summaryPageManager.chartManager.initializeCharts();
    }
};

window.initSummaryTables = function() {
    if (window.summaryPageManager && window.summaryPageManager.tableManager) {
        window.summaryPageManager.tableManager.initializeTables();
    }
};

window.setupChartTypeSelectors = function() {
    if (window.summaryPageManager && window.summaryPageManager.chartManager) {
        window.summaryPageManager.chartManager.setupChartTypeSelectors();
    }
};

window.handleResize = function() {
    if (window.summaryPageManager) {
        window.summaryPageManager.handleResize();
    }
};

window.exportCharts = function() {
    if (window.summaryPageManager) {
        window.summaryPageManager.exportCharts();
    }
};

window.searchCustomer = function() {
    if (window.summaryPageManager) {
        window.summaryPageManager.searchCustomer();
    }
};

window.checkApiStatus = function() {
    if (window.summaryPageManager && window.summaryPageManager.apiClient) {
        window.summaryPageManager.apiClient.checkApiStatus();
    }
};

window.optimizeForMobile = function() {
    if (window.summaryPageManager) {
        window.summaryPageManager.optimizeForMobile();
    }
};