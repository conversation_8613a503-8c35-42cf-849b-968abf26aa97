import logging
from flask import Flask, render_template, redirect, url_for
from flask_login import LoginManager
from flask_caching import Cache
import os
from logging.handlers import RotatingFileHandler
from datetime import timedelta

# 导入配置
from config import config_by_name

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化 LoginManager 和 Cache
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录以访问此页面。'

# 初始化缓存
cache = Cache(config={
    'CACHE_TYPE': 'filesystem',  # 或 'simple'、'redis' 等其他可选类型
    'CACHE_DIR': os.path.join(os.getcwd(), 'cache'),  # 文件系统缓存目录
    'CACHE_DEFAULT_TIMEOUT': 300,  # 默认缓存超时时间，单位秒
    'CACHE_THRESHOLD': 500,  # 最大缓存数量
})

def create_app(config_name='development'):
    """创建Flask应用实例"""
    app = Flask(__name__)
    app.config.from_object(config_by_name[config_name])
    
    # 初始化缓存设置
    app.config['CACHE_TYPE'] = 'SimpleCache'  # 使用内存缓存
    app.config['CACHE_DEFAULT_TIMEOUT'] = 300  # 默认缓存时间5分钟
    
    # 初始化登录管理器
    login_manager.init_app(app)
    
    # 初始化缓存
    cache.init_app(app)
    
    # 配置会话超时
    app.permanent_session_lifetime = timedelta(hours=app.config.get('SESSION_LIFETIME_HOURS', 24))
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    from app.routes.auth import auth as auth_blueprint
    from app.routes.main_refactored import main as main_blueprint
    from app.routes.query_routes import query_bp
    from app.routes.export_routes import export_bp
    from app.routes.api import api as api_blueprint
    from app.routes.order_cleaner import order_cleaner_bp
    from app.routes.async_routes import async_bp
    
    app.register_blueprint(auth_blueprint)
    app.register_blueprint(main_blueprint)
    app.register_blueprint(query_bp)
    app.register_blueprint(export_bp)
    app.register_blueprint(api_blueprint, url_prefix='/api')
    # 注册订单清洗蓝图，添加URL前缀避免冲突
    app.register_blueprint(order_cleaner_bp, url_prefix='/tools')
    # 注册异步任务蓝图
    app.register_blueprint(async_bp)
    
    # 创建缓存目录
    cache_dir = os.path.join(os.getcwd(), 'cache')
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 添加根路由重定向到工作台
    @app.route('/')
    def root():
        return redirect(url_for('main.homepage'))
    
    # 注册自定义过滤器
    from app.utils.helpers import register_template_filters
    register_template_filters(app)
    
    # 添加自定义错误处理
    register_error_handlers(app)
    
    return app

def register_error_handlers(app):
    """注册自定义错误处理器"""
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden(e):
        return render_template('errors/403.html'), 403

# 配置日志
def setup_logging(app):
    # 创建日志目录
    log_dir = os.path.join(os.getcwd(), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s:%(name)s:%(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 创建文件处理器
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'app.log'), 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 将处理器添加到根日志记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    # 设置Flask应用的日志处理
    app.logger.handlers = []  # 移除默认处理器
    for handler in logger.handlers:
        app.logger.addHandler(handler) 