/**
 * 数据加载模块
 * 负责根据URL参数和用户操作加载各种数据
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.ApiDataManager) {
    throw new Error('TaixiangApp Core 和 ApiDataManager 模块未加载');
}

TaixiangApp.DataLoader = {
    // 根据选项卡ID加载相应的数据
    loadTabData: function(tabId) {
        console.log(`加载选项卡数据: ${tabId}`);
        
        switch (tabId) {
            case 'filter':
                this.loadFilterData();
                break;
            case 'overdue':
                this.loadOverdueData();
                break;
            case 'customer':
                this.loadCustomerData();
                break;
            case 'receivable':
                this.loadReceivableData();
                break;
            case 'orders':
                this.loadOrdersData();
                break;
            case 'finance':
                this.loadFinanceData();
                break;
            default:
                console.warn(`未知的选项卡ID: ${tabId}`);
        }
    },

    // 加载客户数据
    loadCustomerData: function() {
        const customerTab = document.getElementById('customer');
        if (!customerTab) {
            console.log('找不到客户标签页容器');
            return;
        }
        
        // 获取客户姓名
        const customerNameInput = document.getElementById('customerName');
        if (!customerNameInput) {
            console.log('找不到客户姓名输入框');
            return;
        }
        
        // 优先从URL参数获取客户姓名
        const urlParams = new URLSearchParams(window.location.search);
        const customerNameFromUrl = urlParams.get('customerName');
        
        // 如果URL中有客户姓名参数，更新输入框的值
        if (customerNameFromUrl) {
            customerNameInput.value = customerNameFromUrl;
        }
        
        // 使用URL参数中的客户姓名或输入框中的客户姓名
        const customerName = customerNameFromUrl || customerNameInput.value;
        if (!customerName) {
            console.log('没有客户姓名，不加载客户数据');
            return;
        }
        
        const params = { customer_name: customerName };
        
        // 检查是否有缓存数据
        const cachedData = TaixiangApp.ApiDataManager.getData('filter_orders_by_customer_name', params);
        if (cachedData) {
            console.log('使用缓存的客户订单数据');
            TaixiangApp.DataDisplay.displayCustomerData(cachedData);
            return;
        }
        
        // 如果没有缓存数据，发起API请求
        console.log(`加载客户${customerName}的订单数据`);
        TaixiangApp.Utils.showLoading();
        
        fetch(`/api/filter_orders_by_customer_name?customer_name=${encodeURIComponent(customerName)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取客户订单数据失败，请重试');
                }
                return response.json();
            })
            .then(data => {
                // 缓存API响应数据
                TaixiangApp.ApiDataManager.storeData('filter_orders_by_customer_name', params, data);
                
                // 显示数据
                TaixiangApp.DataDisplay.displayCustomerData(data);
                TaixiangApp.Utils.hideLoading();
            })
            .catch(error => {
                console.error('客户订单数据加载错误:', error);
                customerTab.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        获取数据失败: ${error.message}
                    </div>
                `;
                TaixiangApp.Utils.hideLoading();
            });
    },

    // 加载日期筛选数据
    loadFilterData: function() {
        const filterTab = document.getElementById('filter');
        if (!filterTab) {
            console.log('找不到筛选标签页容器');
            return;
        }
        
        // 优先从URL参数获取日期
        const urlParams = new URLSearchParams(window.location.search);
        const dateFromUrl = urlParams.get('date');
        
        // 获取筛选日期
        const dateInput = document.getElementById('date');
        if (!dateInput) {
            console.log('找不到日期输入框');
            return;
        }
        
        // 如果URL中有日期参数，更新输入框的值
        if (dateFromUrl) {
            dateInput.value = dateFromUrl;
        }
        
        // 使用URL参数中的日期或输入框中的日期
        const date = dateFromUrl || dateInput.value;
        if (!date) {
            console.log('没有筛选日期，不加载筛选数据');
            return;
        }
        
        const params = { date: date };
        
        // 检查是否有缓存数据
        const cachedData = TaixiangApp.ApiDataManager.getData('filter_data', params);
        if (cachedData) {
            console.log('使用缓存的筛选数据');
            TaixiangApp.DataDisplay.displayFilterData(cachedData);
            return;
        }
        
        // 如果没有缓存数据，发起API请求
        console.log(`加载日期${date}的筛选数据`);
        TaixiangApp.Utils.showLoading();
        
        fetch(`/api/filter_data?date=${encodeURIComponent(date)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取筛选数据失败，请重试');
                }
                return response.json();
            })
            .then(data => {
                // 缓存API响应数据
                TaixiangApp.ApiDataManager.storeData('filter_data', params, data);
                
                // 显示数据
                TaixiangApp.DataDisplay.displayFilterData(data);
                TaixiangApp.Utils.hideLoading();
            })
            .catch(error => {
                console.error('筛选数据加载错误:', error);
                filterTab.innerHTML = `
                    <div class="alert alert-danger mt-3">
                        获取数据失败: ${error.message}
                    </div>
                `;
                TaixiangApp.Utils.hideLoading();
            });
    },

    // 逾期订单数据分页状态
    overdueDataState: {
        currentPage: 1,
        totalPages: 1,
        totalRecords: 0,
        isLoading: false,
        hasMore: true
    },

    // 加载逾期订单数据
    loadOverdueData: function(page = 1, append = false) {
        console.log(`开始加载逾期订单数据，页码: ${page}, 是否追加: ${append}`);
        const overdueTab = document.getElementById('overdue');
        if (!overdueTab) {
            console.error('找不到逾期订单选项卡容器!');
            return;
        }
        
        // 如果正在加载中，则不重复加载
        if (this.overdueDataState.isLoading) {
            console.log('逾期订单数据正在加载中，跳过本次请求');
            return;
        }
        
        // 设置加载状态
        this.overdueDataState.isLoading = true;
        
        // 检查选项卡是否已有数据表格，且不是追加模式
        const existingTable = overdueTab.querySelector('table.data-table');
        if (existingTable && existingTable.rows.length > 1 && !append && page === 1) {
            console.log('逾期订单选项卡已有数据表格，不重新加载');
            this.overdueDataState.isLoading = false;
            TaixiangApp.Utils.hideLoading();
            return;
        }
        
        console.log('需要加载逾期订单数据');
        
        // 如果是第一页且非追加模式，显示加载动画
        if (page === 1 && !append) {
            overdueTab.innerHTML = `
                <div class="p-4 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-primary">正在加载逾期订单数据，请稍候...</p>
                    <p class="small text-muted">大量数据加载可能需要一些时间</p>
                </div>
            `;
        } else if (append) {
            // 追加模式，在表格后添加加载提示
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'overdue-loading-more';
            loadingIndicator.className = 'text-center py-3';
            loadingIndicator.innerHTML = `
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载更多...</span>
                </div>
                <span class="ms-2 text-primary">正在加载更多数据...</span>
            `;
            
            const existingIndicator = document.getElementById('overdue-loading-more');
            if (!existingIndicator) {
                const tableContainer = overdueTab.querySelector('.table-responsive');
                if (tableContainer) {
                    tableContainer.insertAdjacentElement('afterend', loadingIndicator);
                }
            }
        }
        
        // 发起API请求
        const params = { page: page, per_page: 50 };
        
        TaixiangApp.ApiDataManager.fetchApi('overdue_orders', params)
            .then(data => {
                console.log('逾期订单数据加载成功:', data);
                
                // 更新分页状态
                this.overdueDataState.currentPage = page;
                this.overdueDataState.totalPages = data.total_pages || 1;
                this.overdueDataState.totalRecords = data.total_records || 0;
                this.overdueDataState.hasMore = page < this.overdueDataState.totalPages;
                this.overdueDataState.isLoading = false;
                
                // 显示数据
                TaixiangApp.DataDisplay.displayOverdueData(data, append);
                
                // 清除加载指示器
                const loadingIndicator = document.getElementById('overdue-loading-more');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }
                
                TaixiangApp.Utils.hideLoading();
            })
            .catch(error => {
                console.error('逾期订单数据加载错误:', error);
                this.overdueDataState.isLoading = false;
                
                if (page === 1 && !append) {
                    overdueTab.innerHTML = `
                        <div class="alert alert-danger mt-3">
                            获取逾期订单数据失败: ${error.message}
                        </div>
                    `;
                }
                
                TaixiangApp.Utils.hideLoading();
            });
    },

    // 加载更多逾期订单数据
    loadMoreOverdueData: function() {
        if (this.overdueDataState.isLoading || !this.overdueDataState.hasMore) {
            return;
        }
        
        const nextPage = this.overdueDataState.currentPage + 1;
        this.loadOverdueData(nextPage, true);
    },

    // 重置逾期数据状态
    resetOverdueState: function() {
        this.overdueDataState = {
            currentPage: 1,
            totalPages: 1,
            totalRecords: 0,
            isLoading: false,
            hasMore: true
        };
    },

    // 加载待收明细数据
    loadReceivableData: function() {
        console.log('加载待收明细数据');
        const receivableTab = document.getElementById('receivable');
        if (!receivableTab) {
            console.log('找不到待收明细标签页容器');
            return;
        }

        // 企业级客户汇总页面的待收明细已经由服务端渲染，无需额外加载
        console.log('待收明细数据已由服务端渲染完成');
    },

    // 加载订单详情数据
    loadOrdersData: function() {
        console.log('加载订单详情数据');
        const ordersTab = document.getElementById('orders');
        if (!ordersTab) {
            console.log('找不到订单详情标签页容器');
            return;
        }

        // 企业级客户汇总页面的订单详情已经由服务端渲染，无需额外加载
        console.log('订单详情数据已由服务端渲染完成');
    },

    // 加载财务流水数据
    loadFinanceData: function() {
        console.log('加载财务流水数据');
        const financeTab = document.getElementById('finance');
        if (!financeTab) {
            console.log('找不到财务流水标签页容器');
            return;
        }

        // 企业级客户汇总页面的财务流水已经由服务端渲染，无需额外加载
        console.log('财务流水数据已由服务端渲染完成');
    }
};

// 向后兼容的全局函数
window.loadTabData = TaixiangApp.DataLoader.loadTabData.bind(TaixiangApp.DataLoader);
window.loadCustomerData = TaixiangApp.DataLoader.loadCustomerData.bind(TaixiangApp.DataLoader);
window.loadFilterData = TaixiangApp.DataLoader.loadFilterData.bind(TaixiangApp.DataLoader);
window.loadOverdueData = TaixiangApp.DataLoader.loadOverdueData.bind(TaixiangApp.DataLoader);
window.loadMoreOverdueData = TaixiangApp.DataLoader.loadMoreOverdueData.bind(TaixiangApp.DataLoader);

// 将状态同步到全局
window.overdueDataState = TaixiangApp.DataLoader.overdueDataState;

console.log('数据加载模块已加载'); 