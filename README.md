# 太享查询系统

太享查询系统是一个基于Flask框架的金融数据查询展示系统，用于展示和分析从API获取的金融数据。

## 快速开始

### 开发环境
```bash
# 克隆项目
git clone <repository-url>
cd hdsc_query_app

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动应用
python run.py
```

### 生产部署
```bash
chmod +x deploy.sh
sudo ./deploy.sh
```

访问地址：http://localhost:5000

## 主要功能

- 🔐 **用户权限控制** - 三级权限系统
- 📊 **数据查询分析** - 日期筛选、客户查询、逾期订单
- 📈 **数据可视化** - 图表展示、汇总分析
- 📋 **数据导出** - Excel、CSV格式
- 🧮 **实用工具** - 计算器、二维码生成、合同生成

## 用户权限

- **有限权限**: `TT2024`
- **标准权限**: `881017` 
- **完全权限**: `Doolin`

## 详细文档

- 📖 **[项目部署运维指南](./项目部署运维指南.md)** - 完整的部署和运维说明
- 🛠️ **[功能实现说明](./功能实现说明.md)** - 各功能模块的详细说明
- 🔧 **[技术修复指南](./技术修复指南.md)** - 技术问题修复和系统重构
- 🗄️ **[数据库接口文档](./数据库接口文档.md)** - API接口说明

## 技术栈

- **后端**: Flask, Pandas, Requests
- **前端**: Bootstrap 5, Chart.js, DataTables
- **部署**: Gunicorn, Nginx, Systemd

## 环境要求

- Python 3.8+
- Ubuntu Server (推荐)
- 依赖包详见 `requirements.txt`

## 联系方式

如有问题，请联系系统管理员或查看详细文档。

---
**最后更新**: 2024年12月  
**维护团队**: 太享查询系统开发组