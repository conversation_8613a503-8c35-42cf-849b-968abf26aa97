/**
 * 性能优化模块
 * 提供缓存管理、资源优化、懒加载和性能监控功能
 */

class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.cacheConfig = {
            maxSize: 50, // 最大缓存项数
            maxAge: 30 * 60 * 1000, // 30分钟缓存时间
            cleanupInterval: 5 * 60 * 1000 // 5分钟清理间隔
        };
        
        this.performanceMetrics = {
            pageLoadTime: 0,
            apiCallTimes: [],
            chartRenderTimes: [],
            tableRenderTimes: [],
            memoryUsage: []
        };
        
        this.observers = {
            intersection: null,
            mutation: null,
            performance: null
        };
        
        this.optimizationConfig = {
            enableLazyLoading: true,
            enableImageOptimization: true,
            enableCodeSplitting: true,
            enableMemoryOptimization: true,
            enablePerformanceMonitoring: true
        };
        
        // 绑定方法上下文
        this.debounce = this.debounce.bind(this);
        this.throttle = this.throttle.bind(this);
        this.measurePerformance = this.measurePerformance.bind(this);
    }

    /**
     * 初始化性能优化器
     * @param {Object} config - 配置选项
     */
    initialize(config = {}) {
        this.optimizationConfig = { ...this.optimizationConfig, ...config };
        
        console.log('PerformanceOptimizer: 性能优化器已初始化');
        
        // 开始性能监控
        if (this.optimizationConfig.enablePerformanceMonitoring) {
            this.startPerformanceMonitoring();
        }
        
        // 初始化缓存清理
        this.startCacheCleanup();
        
        // 设置懒加载
        if (this.optimizationConfig.enableLazyLoading) {
            this.setupLazyLoading();
        }
        
        // 内存优化
        if (this.optimizationConfig.enableMemoryOptimization) {
            this.setupMemoryOptimization();
        }
        
        // 图片优化
        if (this.optimizationConfig.enableImageOptimization) {
            this.optimizeImages();
        }
    }

    /**
     * 缓存管理
     */
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} maxAge - 可选的过期时间
     */
    setCache(key, value, maxAge = this.cacheConfig.maxAge) {
        const cacheItem = {
            value,
            timestamp: Date.now(),
            maxAge
        };
        
        this.cache.set(key, cacheItem);
        
        // 检查缓存大小限制
        if (this.cache.size > this.cacheConfig.maxSize) {
            this.evictOldestCache();
        }
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {*} 缓存值或null
     */
    getCache(key) {
        const cacheItem = this.cache.get(key);
        
        if (!cacheItem) {
            return null;
        }
        
        // 检查是否过期
        const isExpired = Date.now() - cacheItem.timestamp > cacheItem.maxAge;
        if (isExpired) {
            this.cache.delete(key);
            return null;
        }
        
        return cacheItem.value;
    }

    /**
     * 清除缓存
     * @param {string} key - 缓存键，如果不提供则清除所有缓存
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * 驱逐最旧的缓存项
     */
    evictOldestCache() {
        let oldestKey = null;
        let oldestTimestamp = Date.now();
        
        for (const [key, item] of this.cache.entries()) {
            if (item.timestamp < oldestTimestamp) {
                oldestTimestamp = item.timestamp;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }

    /**
     * 开始缓存清理
     */
    startCacheCleanup() {
        setInterval(() => {
            const now = Date.now();
            const keysToDelete = [];
            
            for (const [key, item] of this.cache.entries()) {
                if (now - item.timestamp > item.maxAge) {
                    keysToDelete.push(key);
                }
            }
            
            keysToDelete.forEach(key => this.cache.delete(key));
            
            if (keysToDelete.length > 0) {
                console.log(`PerformanceOptimizer: 清理了 ${keysToDelete.length} 个过期缓存项`);
            }
        }, this.cacheConfig.cleanupInterval);
    }

    /**
     * 性能监控
     */
    
    /**
     * 开始性能监控
     */
    startPerformanceMonitoring() {
        // 页面加载时间
        this.measurePageLoadTime();
        
        // 内存使用监控
        this.startMemoryMonitoring();
        
        // 性能观察器
        this.setupPerformanceObserver();
    }

    /**
     * 测量页面加载时间
     */
    measurePageLoadTime() {
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            this.performanceMetrics.pageLoadTime = timing.loadEventEnd - timing.navigationStart;
            
            console.log(`PerformanceOptimizer: 页面加载时间 ${this.performanceMetrics.pageLoadTime}ms`);
        }
    }

    /**
     * 开始内存监控
     */
    startMemoryMonitoring() {
        setInterval(() => {
            if (window.performance && window.performance.memory) {
                const memory = window.performance.memory;
                const memoryInfo = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };
                
                this.performanceMetrics.memoryUsage.push(memoryInfo);
                
                // 保留最近的50个记录
                if (this.performanceMetrics.memoryUsage.length > 50) {
                    this.performanceMetrics.memoryUsage = this.performanceMetrics.memoryUsage.slice(-50);
                }
                
                // 内存使用率警告
                const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                if (usagePercent > 80) {
                    console.warn(`PerformanceOptimizer: 内存使用率较高 ${usagePercent.toFixed(2)}%`);
                }
            }
        }, 30000); // 每30秒检查一次
    }

    /**
     * 设置性能观察器
     */
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.processPerformanceEntry(entry);
                }
            });
            
            try {
                observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
                this.observers.performance = observer;
            } catch (err) {
                console.warn('PerformanceOptimizer: PerformanceObserver不支持某些类型');
            }
        }
    }

    /**
     * 处理性能条目
     * @param {PerformanceEntry} entry - 性能条目
     */
    processPerformanceEntry(entry) {
        switch (entry.entryType) {
            case 'measure':
                if (entry.name.includes('api-call')) {
                    this.performanceMetrics.apiCallTimes.push(entry.duration);
                } else if (entry.name.includes('chart-render')) {
                    this.performanceMetrics.chartRenderTimes.push(entry.duration);
                } else if (entry.name.includes('table-render')) {
                    this.performanceMetrics.tableRenderTimes.push(entry.duration);
                }
                break;
                
            case 'resource':
                if (entry.duration > 2000) { // 资源加载超过2秒
                    console.warn(`PerformanceOptimizer: 资源加载缓慢 ${entry.name} - ${entry.duration.toFixed(2)}ms`);
                }
                break;
        }
    }

    /**
     * 测量性能
     * @param {string} name - 测量名称
     * @param {Function} func - 要测量的函数
     * @returns {*} 函数执行结果
     */
    async measurePerformance(name, func) {
        const startMark = `${name}-start`;
        const endMark = `${name}-end`;
        const measureName = `${name}-duration`;
        
        performance.mark(startMark);
        
        try {
            const result = await func();
            performance.mark(endMark);
            performance.measure(measureName, startMark, endMark);
            
            return result;
        } catch (error) {
            performance.mark(endMark);
            performance.measure(measureName, startMark, endMark);
            throw error;
        } finally {
            // 清理标记
            performance.clearMarks(startMark);
            performance.clearMarks(endMark);
        }
    }

    /**
     * 懒加载设置
     */
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.observers.intersection = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadLazyElement(entry.target);
                        this.observers.intersection.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px'
            });
            
            // 观察懒加载元素
            document.querySelectorAll('[data-lazy]').forEach(element => {
                this.observers.intersection.observe(element);
            });
        }
    }

    /**
     * 加载懒加载元素
     * @param {HTMLElement} element - 元素
     */
    loadLazyElement(element) {
        const lazyType = element.dataset.lazy;
        
        switch (lazyType) {
            case 'image':
                this.loadLazyImage(element);
                break;
            case 'chart':
                this.loadLazyChart(element);
                break;
            case 'table':
                this.loadLazyTable(element);
                break;
        }
    }

    /**
     * 加载懒加载图片
     * @param {HTMLElement} img - 图片元素
     */
    loadLazyImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.add('loaded');
        }
    }

    /**
     * 内存优化
     */
    setupMemoryOptimization() {
        // 页面可见性变化时优化内存
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.optimizeMemoryOnHide();
            } else {
                this.restoreOnShow();
            }
        });
        
        // 定期清理未使用的对象
        setInterval(() => {
            this.cleanupUnusedObjects();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 页面隐藏时的内存优化
     */
    optimizeMemoryOnHide() {
        // 暂停不必要的定时器
        // 清理大型对象的引用
        // 释放图表实例
        console.log('PerformanceOptimizer: 页面隐藏，执行内存优化');
    }

    /**
     * 页面显示时的恢复
     */
    restoreOnShow() {
        // 恢复必要的定时器
        // 重新初始化必要的组件
        console.log('PerformanceOptimizer: 页面显示，恢复组件');
    }

    /**
     * 清理未使用的对象
     */
    cleanupUnusedObjects() {
        // 强制垃圾回收（如果浏览器支持）
        if (window.gc) {
            window.gc();
        }
        
        // 清理过期的事件监听器
        this.cleanupEventListeners();
    }

    /**
     * 清理事件监听器
     */
    cleanupEventListeners() {
        // 移除已分离DOM元素的事件监听器
        // 这是一个示例实现，实际应用中需要根据具体情况调整
    }

    /**
     * 图片优化
     */
    optimizeImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // 添加加载优化
            img.loading = 'lazy';
            
            // 添加解码优化
            img.decoding = 'async';
            
            // 监听加载错误
            img.addEventListener('error', () => {
                console.warn(`PerformanceOptimizer: 图片加载失败 ${img.src}`);
            });
        });
    }

    /**
     * 工具函数
     */
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 批处理DOM操作
     * @param {Function} operations - DOM操作函数
     */
    batchDOMOperations(operations) {
        requestAnimationFrame(() => {
            operations();
        });
    }

    /**
     * 预加载资源
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     */
    preloadResource(url, type = 'fetch') {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = url;
        link.as = type;
        document.head.appendChild(link);
    }

    /**
     * 获取性能报告
     * @returns {Object} 性能报告
     */
    getPerformanceReport() {
        const report = {
            pageLoadTime: this.performanceMetrics.pageLoadTime,
            averageApiCallTime: this.calculateAverage(this.performanceMetrics.apiCallTimes),
            averageChartRenderTime: this.calculateAverage(this.performanceMetrics.chartRenderTimes),
            averageTableRenderTime: this.calculateAverage(this.performanceMetrics.tableRenderTimes),
            cacheHitRate: this.calculateCacheHitRate(),
            memoryUsage: this.getMemoryUsageSummary()
        };
        
        return report;
    }

    /**
     * 计算平均值
     * @param {Array} values - 数值数组
     * @returns {number} 平均值
     */
    calculateAverage(values) {
        if (values.length === 0) return 0;
        return values.reduce((sum, value) => sum + value, 0) / values.length;
    }

    /**
     * 计算缓存命中率
     * @returns {number} 缓存命中率
     */
    calculateCacheHitRate() {
        // 这里应该根据实际的缓存命中统计计算
        return 0; // 占位符
    }

    /**
     * 获取内存使用摘要
     * @returns {Object} 内存使用摘要
     */
    getMemoryUsageSummary() {
        const recent = this.performanceMetrics.memoryUsage.slice(-10);
        if (recent.length === 0) return null;
        
        const latest = recent[recent.length - 1];
        return {
            current: latest.used,
            total: latest.total,
            limit: latest.limit,
            usagePercent: (latest.used / latest.limit) * 100
        };
    }

    /**
     * 销毁性能优化器
     */
    destroy() {
        // 清理观察器
        Object.values(this.observers).forEach(observer => {
            if (observer) {
                observer.disconnect();
            }
        });
        
        // 清理缓存
        this.clearCache();
        
        console.log('PerformanceOptimizer: 性能优化器已销毁');
    }
}

// 导出模块
window.PerformanceOptimizer = PerformanceOptimizer;