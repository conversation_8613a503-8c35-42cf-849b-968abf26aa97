/**
 * 导航管理模块
 * 负责页面跳转、路由管理和URL参数处理
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils) {
    throw new Error('TaixiangApp Core 模块未加载，请先加载 app-core.js');
}

// 全局统一导航与功能跳转管理
TaixiangApp.Navigation = {
    // 跳转到首页
    goToHome: function() {
        window.location.href = "/";
    },
    
    // 跳转到新工作台首页
    goToWorkbench: function() {
        window.location.href = "/home";
    },
    
    // 日期筛选
    filterByDate: function(date) {
        if (!date) {
            const dateInput = document.getElementById('date');
            if (!dateInput || !dateInput.value) {
                alert('请选择日期');
                return;
            }
            date = dateInput.value;
        }
        
        // 检查当前页面
        const currentPath = window.location.pathname;
        if (currentPath === '/') {
            // 在首页直接更新URL参数
            const url = new URL(window.location.href);
            url.searchParams.set('date', date);
            url.searchParams.set('tab', 'filter');
            window.location.href = url.toString();
        } else {
            // 在其他页面跳转到首页并带参数
            window.location.href = `/?date=${encodeURIComponent(date)}&tab=filter`;
        }
    },
    
    // 客户搜索
    searchCustomer: function() {
        const customerName = document.getElementById('customerName').value;
        if (!customerName) {
            alert('请输入客户姓名');
            return;
        }
        
        // 检查当前页面
        const currentPath = window.location.pathname;
        if (currentPath === '/') {
            // 在首页直接更新URL参数
            const url = new URL(window.location.href);
            url.searchParams.set('customerName', customerName);
            url.searchParams.set('tab', 'customer');
            window.location.href = url.toString();
        } else {
            // 在其他页面跳转到首页并带参数
            window.location.href = `/?customerName=${encodeURIComponent(customerName)}&tab=customer`;
        }
    },
    
    // 逾期订单查询
    showOverdueOrders: function() {
        // 直接跳转到独立的逾期订单页面
        window.location.href = '/overdue';
    },
    
    // 数据汇总视图
    showSummaryView: function() {
        window.location.href = "/summary";
    },
    
    // 批量回执单生成器
    showBatchReceiptGenerator: function() {
        window.location.href = "/batch_receipt_generator";
    },
    
    // 退出登录
    logout: function() {
        window.location.href = "/logout";
    },
    
    // 检查API状态
    checkApiStatus: function() {
        // 委托给API状态管理器
        if (window.apiStatusManager) {
            window.apiStatusManager.manualCheck();
        } else {
            console.warn('API状态管理器未加载');
        }
    },
    
    // 下载Windows版
    downloadWindowsVersion: function() {
        try {
            // 显示下载提示
            TaixiangApp.Utils.showLoading('正在准备下载...');
            
            // 创建隐藏的下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = '/download/windows';
            downloadLink.download = '太享查询.exe';
            downloadLink.style.display = 'none';
            
            // 添加到页面并触发点击
            document.body.appendChild(downloadLink);
            downloadLink.click();
            
            // 清理
            document.body.removeChild(downloadLink);
            
            // 延迟隐藏加载提示
            setTimeout(() => {
                TaixiangApp.Utils.hideLoading();
                
                // 显示下载成功提示
                if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                    const toastHtml = `
                        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="bi bi-check-circle me-2"></i>
                                    Windows客户端下载已开始，请查看浏览器下载文件夹
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                            </div>
                        </div>
                    `;
                    
                    // 创建toast容器（如果不存在）
                    let toastContainer = document.getElementById('toast-container');
                    if (!toastContainer) {
                        toastContainer = document.createElement('div');
                        toastContainer.id = 'toast-container';
                        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                        toastContainer.style.zIndex = '9999';
                        document.body.appendChild(toastContainer);
                    }
                    
                    // 添加toast并显示
                    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
                    const toastElement = toastContainer.lastElementChild;
                    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
                    toast.show();
                    
                    // 自动清理toast元素
                    toastElement.addEventListener('hidden.bs.toast', () => {
                        toastElement.remove();
                    });
                } else {
                    // 降级到alert
                    alert('Windows客户端下载已开始，请查看浏览器下载文件夹');
                }
            }, 1000);
            
        } catch (error) {
            console.error('下载Windows客户端失败:', error);
            TaixiangApp.Utils.hideLoading();
            alert('下载失败，请重试或联系管理员');
        }
    },
    
    // 获取URL参数
    getUrlParams: function() {
        return new URLSearchParams(window.location.search);
    },
    
    // 更新URL参数但不刷新页面
    updateUrlParams: function(params) {
        const url = new URL(window.location.href);
        
        Object.keys(params).forEach(key => {
            if (params[key] === null || params[key] === undefined) {
                url.searchParams.delete(key);
            } else {
                url.searchParams.set(key, params[key]);
            }
        });
        
        // 使用History API更新URL，不刷新页面
        window.history.replaceState({}, '', url.toString());
        
        return url.searchParams;
    },
    
    // 激活指定的标签页
    activateTab: function(tabId) {
        const tabElement = document.getElementById(tabId + '-tab');
        if (tabElement) {
            const tab = new bootstrap.Tab(tabElement);
            tab.show();
            console.log('已激活选项卡: ' + tabId);
            return true;
        }
        return false;
    }
};

// 客户搜索相关功能
TaixiangApp.CustomerSearch = {
    // 全局统一的客户搜索函数
    globalSearchCustomer: function() {
        const customerNameInput = document.getElementById('customerName');
        if (!customerNameInput || !customerNameInput.value.trim()) {
            alert('请输入客户姓名');
            return;
        }
        
        const customerName = customerNameInput.value.trim();
        window.location.href = "/?customerName=" + encodeURIComponent(customerName) + "&tab=customer";
    },
    
    // 查看客户汇总数据
    viewCustomerSummary: function(customerName) {
        if (!customerName) {
            alert('客户姓名不能为空');
            return;
        }
        
        // 显示加载指示器
        TaixiangApp.Utils.showLoading();
        
        // 检查缓存中是否有数据
        const params = { customer_name: customerName };
        const cachedData = TaixiangApp.ApiDataManager.getData('customer_summary', params);
        
        if (cachedData) {
            // 使用缓存的数据
            console.log('使用缓存的客户汇总数据');
            
            // 存储数据到localStorage以便在页面跳转后使用
            localStorage.setItem('customerSummary', JSON.stringify(cachedData));
            
            // 跳转到客户汇总页面
            window.location.href = `/customer_summary/${encodeURIComponent(customerName)}`;
            return;
        }
        
        // 发送API请求获取客户汇总数据
        fetch(`/api/customer_summary?customer_name=${encodeURIComponent(customerName)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取汇总数据失败，请重试');
                }
                return response.json();
            })
            .then(data => {
                // 缓存API响应数据
                TaixiangApp.ApiDataManager.storeData('customer_summary', params, data);
                
                // 存储数据到localStorage以便在页面跳转后使用
                localStorage.setItem('customerSummary', JSON.stringify(data));
                
                // 跳转到客户汇总页面
                window.location.href = `/customer_summary/${encodeURIComponent(customerName)}`;
            })
            .catch(error => {
                console.error('客户汇总数据错误:', error);
                alert(`获取汇总数据失败: ${error.message}`);
                TaixiangApp.Utils.hideLoading();
            });
    }
};

// 快速搜索功能（支持首页quickSearch输入框）
TaixiangApp.QuickSearch = {
    // 快速搜索处理函数
    handleQuickSearch: function() {
        const quickSearchInput = document.getElementById('quickSearch');
        if (!quickSearchInput || !quickSearchInput.value.trim()) {
            alert('请输入客户姓名或手机号');
            return;
        }
        
        const searchValue = quickSearchInput.value.trim();
        
        // 跳转到首页并带搜索参数
        window.location.href = `/?customerName=${encodeURIComponent(searchValue)}&tab=customer`;
    }
};

// 向后兼容的全局对象和函数
window.AppNavigation = TaixiangApp.Navigation;
window.QuickSearch = TaixiangApp.QuickSearch;
window.globalSearchCustomer = TaixiangApp.CustomerSearch.globalSearchCustomer;
window.viewCustomerSummary = TaixiangApp.CustomerSearch.viewCustomerSummary;

console.log('导航管理模块已加载');