/**
 * 导出功能管理模块
 * 统一管理所有与数据导出相关的功能
 */
const ExportManager = {
    // 全局配置选项
    config: {
        // 文件名前缀，默认为“数据导出”
        fileNamePrefix: '数据导出',
        
        // 是否在文件名中包含时间戳
        includeTimestamp: true,
        
        // 是否在导出前显示确认对话框
        confirmBeforeExport: false,
        
        // 导出成功后是否显示成功消息
        showSuccessMessage: false
    },
    
    // 数据源处理器集合
    dataSourceHandlers: {},
    
    /**
     * 初始化导出管理器
     * @param {Object} options - 配置选项
     */
    init: function(options = {}) {
        // 合并用户自定义配置
        this.config = Object.assign({}, this.config, options);
        console.log('导出管理器初始化完成，配置:', this.config);
        return this;
    },
    /**
     * 添加自定义数据源处理器
     * @param {string} dataSourceId - 数据源标识
     * @param {Function} handler - 处理函数，返回Promise
     */
    addDataSourceHandler: function(dataSourceId, handler) {
        if (typeof handler !== 'function') {
            console.error(`数据源处理器必须是函数: ${dataSourceId}`);
            return;
        }
        
        this.dataSourceHandlers[dataSourceId] = handler;
        console.log(`已注册数据源处理器: ${dataSourceId}`);
    },
    
    /**
     * 导出数据（支持筛选和搜索）
     * @param {string} format - 导出格式，可选值：'excel' 或 'csv'
     * @param {Object} options - 导出选项
     * @param {string} options.dataSource - 自定义数据源ID
     * @param {string} options.tabId - 当前活动选项卡ID
     * @param {HTMLElement} options.table - 表格元素
     * @param {string} options.searchQuery - 搜索查询字符串
     * @param {string} options.title - 要导出的数据标题，将用于文件名
     * @returns {Promise} 返回Promise对象，便于链式调用
     */
    exportData: function(format, options = {}) {
        console.log(`导出数据，格式: ${format}`);
        const { dataSource, tabId, table, searchQuery = '', title = '' } = options;
        
        // 验证格式
        if (!['excel', 'csv'].includes(format)) {
            const error = new Error(`不支持的导出格式: ${format}`);
            console.error(error);
            alert(error.message);
            return Promise.reject(error);
        }
        
        // 如果指定了自定义数据源，且已注册相应处理器
        if (dataSource && this.dataSourceHandlers[dataSource]) {
            console.log(`使用自定义数据源: ${dataSource}`);
            // 调用自定义数据源处理器
            return this.dataSourceHandlers[dataSource](format, options)
                .then(result => {
                    // 如果返回的是Blob对象，创建下载链接
                    if (result instanceof Blob) {
                        const fileName = this.generateFileName(format, searchQuery, title);
                        return this.createDownloadLink(result, fileName);
                    } 
                    // 如果返回的是对象，包含fileName和data属性
                    else if (result && result.data && result.fileName) {
                        return this.createDownloadLink(result.data, result.fileName);
                    }
                    // 如果处理器返回了普通数据
                    else if (result && Array.isArray(result)) {
                        return this.sendExportRequest(format, result, searchQuery, title);
                    }
                    
                    // 其他情况，返回处理结果
                    return result;
                })
                .catch(error => {
                    console.error('数据源处理器错误:', error);
                    if (error.message !== '用户取消导出') {  // 用户主动取消不显示错误
                        alert(`导出失败: ${error.message || '未知错误'}`);
                    }
                    return Promise.reject(error);
                });
        }
        // 如果提供了表格并且已初始化为DataTable，获取筛选后的数据
        else if (table && $.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();
            const actualSearchQuery = searchQuery || dataTable.search();
            
            console.log(`使用表格筛选，搜索条件: ${actualSearchQuery || '无'}`);
            const filteredData = this.getFilteredDataFromTable(dataTable);
            
            if (filteredData.length === 0) {
                const error = new Error('筛选后没有可导出的数据');
                alert(error.message);
                return Promise.reject(error);
            }
            
            console.log(`导出${filteredData.length}条筛选后的数据`);
            return this.sendExportRequest(format, filteredData, actualSearchQuery, title);
        } 
        // 否则使用全局数据
        else {
            const dataToExport = this.getGlobalData(tabId);
            
            if (!dataToExport || !dataToExport.results || dataToExport.results.length === 0) {
                const error = new Error('当前没有可导出的数据');
                alert(error.message);
                return Promise.reject(error);
            }
            
            console.log(`导出所有数据，数据源: ${tabId}`);
            return this.sendExportRequest(format, dataToExport.results, searchQuery, title);
        }
    },
    
    /**
     * 导出汇总数据
     * @param {string} format - 导出格式，可选值：'excel' 或 'csv'
     * @param {Object} summaryData - 汇总数据
     * @param {string} customerName - 客户名称（可选）
     * @returns {Promise} 返回Promise对象
     */
    exportSummaryData: function(format = 'excel', summaryData, customerName = '') {
        if (!summaryData) {
            // 尝试从全局变量获取汇总数据
            summaryData = window.summaryData;
        }
        
        if (!summaryData) {
            const error = new Error('没有汇总数据可导出');
            alert(error.message);
            return Promise.reject(error);
        }
        
        // 生成一个标题，包含客户名称（如果提供了）
        const title = customerName ? `客户汇总_${customerName}` : '汇总数据';
        
        console.log(`导出汇总数据: ${title}`);
        return this.sendExportRequest(format, summaryData, '', title);
    },
    
    /**
     * 从表格中获取筛选后的数据
     * @param {Object} dataTable - DataTables实例
     * @returns {Array} 筛选后的数据
     */
    getFilteredDataFromTable: function(dataTable) {
        const filteredData = [];
        const self = this;
        
        dataTable.rows({search: 'applied'}).every(function() {
            const rowData = this.data();
            let rowObj = {};
            
            // 检查数据类型并相应处理
            if (Array.isArray(rowData)) {
                // 如果是数组，使用列索引作为键
                const columns = dataTable.columns().header().toArray().map(col => $(col).text());
                for (let i = 0; i < rowData.length; i++) {
                    // 跳过第一列（控制列）
                    if (i > 0) {
                        rowObj[columns[i]] = self.stripHtml(rowData[i]);
                    }
                }
            } else if (typeof rowData === 'object') {
                // 如果是对象，直接使用，但清理HTML标签
                for (const key in rowData) {
                    if (key !== '') { // 跳过控制列
                        rowObj[key] = self.stripHtml(rowData[key]);
                    }
                }
            }
            
            filteredData.push(rowObj);
        });
        
        return filteredData;
    },
    
    /**
     * 获取全局数据
     * @param {string} tabId - 选项卡ID，如 'filter', 'overdue', 'customer'
     * @returns {Object} 全局数据对象
     */
    getGlobalData: function(tabId) {
        switch (tabId) {
            case 'filter':
                return window.filterResults;
            case 'overdue':
                return window.overdueResults;
            case 'customer':
                return window.customerResults;
            case 'summary':
                return window.summaryData;
            default:
                return null;
        }
    },
    
    /**
     * 确认导出操作
     * @param {number|string} dataSize - 数据量大小或格式
     * @returns {Promise<boolean>} 用户确认返回true的Promise，取消返回false的Promise
     */
    confirmExport: function(dataSize) {
        // 如果未启用确认，直接返回成功Promise
        if (!this.config.confirmBeforeExport) {
            return Promise.resolve(true);
        }
        
        // 如果参数是数字，认为是数据量，显示包含数据量的确认提示
        if (typeof dataSize === 'number' && dataSize > 1000) {
            return new Promise(resolve => {
                const confirmed = confirm(`将导出全部 ${dataSize} 条数据，可能需要一些时间。是否继续？`);
                resolve(confirmed);
            });
        } else {
            // 普通确认提示
            return new Promise(resolve => {
                const confirmed = confirm(`确定要导出数据吗？`);
                resolve(confirmed);
            });
        }
    },
    
    /**
     * 生成导出文件名
     * @param {string} format - 导出格式
     * @param {string} searchQuery - 搜索查询字符串
     * @param {string} title - 自定义标题，当提供时使用该标题替代默认前缀
     * @returns {string} 生成的完整文件名
     */
    generateFileName: function(format, searchQuery = '', title = '') {
        // 决定文件名前缀
        let prefix = title || this.config.fileNamePrefix;
        
        // 如果有搜索查询，可以添加到文件名中
        if (searchQuery) {
            // 将搜索查询字符串限制为20个字符，避免文件名过长
            const shortQuery = searchQuery.length > 20 ? searchQuery.substring(0, 20) + '...' : searchQuery;
            prefix += `_搜索_${shortQuery}`;
        }
        
        // 如果配置中要求包含时间戳
        let timestamp = '';
        if (this.config.includeTimestamp) {
            timestamp = `_${new Date().toISOString().slice(0, 10)}`;
        }
        
        // 文件扩展名
        const extension = format === 'excel' ? 'xlsx' : 'csv';
        
        // 组合成完整文件名
        return `${prefix}${timestamp}.${extension}`;
    },
    
    /**
     * 创建下载链接
     * @param {Blob} blob - 要下载的二进制数据
     * @param {string} fileName - 文件名
     * @returns {Promise} 解决为下载完成的Promise
     */
    createDownloadLink: function(blob, fileName) {
        return new Promise((resolve, reject) => {
            try {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 如果配置了显示成功消息，则显示
                if (this.config.showSuccessMessage) {
                    // 使用LoadingController显示成功消息，如果存在
                    if (window.LoadingController) {
                        LoadingController.showSuccess(`导出成功！文件已保存为: ${fileName}`);
                    } else {
                        alert(`导出成功！文件已保存为: ${fileName}`);
                    }
                }
                
                // 导出成功，解决Promise
                resolve();
            } catch (error) {
                console.error('创建下载链接错误:', error);
                if (window.LoadingController) {
                    LoadingController.showError(`导出失败: ${error.message || '未知错误'}`);
                } else {
                    alert(`导出失败: ${error.message || '未知错误'}`);
                }
                reject(error);
            }
        });
    },
    
    /**
     * 发送导出请求到服务器
     * @param {string} format - 导出格式
     * @param {Array} data - 要导出的数据
     * @param {string} searchQuery - 搜索查询字符串
     * @param {string} title - 自定义标题，将用于文件名
     * @returns {Promise} 返回Promise对象，便于链式调用
     */
    sendExportRequest: function(format, data, searchQuery = '', title = '') {
        console.log(`发送导出请求，格式: ${format}, 搜索条件: ${searchQuery || '无'}, 标题: ${title || '无'}`);
        
        // 显示加载状态
        if (window.LoadingController) {
            LoadingController.showLoading('正在准备导出数据...');
        }
        
        return new Promise((resolve, reject) => {
            fetch(`/export/${format}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data: data,
                    search_query: searchQuery,
                    title: title
                }),
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('导出失败');
            })
            .then(blob => {
                // 生成文件名
                let fileName = this.generateFileName(format, searchQuery, title);
                // 使用统一的下载链接创建函数
                return this.createDownloadLink(blob, fileName);
            })
            .then(() => {
                resolve();
            })
            .catch(error => {
                console.error('导出错误:', error);
                
                // 隐藏加载状态
                if (window.LoadingController) {
                    LoadingController.showError(`导出失败: ${error.message || '未知错误'}`);
                } else {
                    alert(`导出失败: ${error.message || '未知错误'}`);
                }
                
                // 导出失败，拒绝Promise
                reject(error);
            });
        });
    },
    
    /**
     * 从HTML内容中提取纯文本
     * @param {string} html - HTML内容
     * @returns {string} 纯文本
     */
    stripHtml: function(html) {
        // 如果不是字符串，直接返回
        if (typeof html !== 'string') {
            return html;
        }
        
        // 创建临时DOM元素提取文本
        const temp = document.createElement('div');
        temp.innerHTML = html;
        return temp.textContent || temp.innerText || '';
    }
};

// 导出为全局对象
window.ExportManager = ExportManager;
