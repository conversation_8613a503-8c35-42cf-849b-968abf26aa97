/**
 * 数据汇总页面图表控制器
 * 实现业务逻辑与视图层的完全分离
 * 
 * <AUTHOR> Assistant
 * @version 2.0.0
 * @since 2024-01-XX
 */

class SummaryChartController {
    constructor() {
        this.chartEngine = window.ChartEngine;
        this.orderChart = null;
        this.overdueChart = null;
        this.isInitialized = false;
        this.defaultData = this.generateDefaultData();
        
        // 绑定方法的this上下文
        this.init = this.init.bind(this);
        this.switchChartType = this.switchChartType.bind(this);
        this.refreshCharts = this.refreshCharts.bind(this);
    }
    
    /**
     * 初始化图表控制器
     * 企业级初始化流程
     */
    async init() {
        try {
            console.log('🚀 初始化数据汇总图表控制器...');
            
            // 等待图表引擎就绪
            await this.chartEngine.whenReady();
            
            // 验证DOM元素存在
            this.validateDOMElements();
            
            // 获取服务器数据
            const serverData = await this.fetchServerData();
            
            // 创建图表
            await this.createOrderChart(serverData.orderData);
            await this.createOverdueChart(serverData.overdueData);
            
            // 绑定事件监听器
            this.bindEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 数据汇总图表控制器初始化完成');
            
        } catch (error) {
            console.error('❌ 图表控制器初始化失败:', error);
            await this.handleInitializationError(error);
        }
    }
    
    /**
     * 验证DOM元素存在性
     */
    validateDOMElements() {
        const requiredElements = ['orderChart', 'overdueChart'];
        const missingElements = [];
        
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                missingElements.push(id);
            }
        });
        
        if (missingElements.length > 0) {
            throw new Error(`缺少必要的Canvas元素: ${missingElements.join(', ')}`);
        }
    }
    
    /**
     * 获取服务器数据（带降级策略）
     * @returns {Promise<Object>} 图表数据
     */
    async fetchServerData() {
        try {
            // 尝试从页面变量获取数据
            if (typeof chartData !== 'undefined' && chartData) {
                console.log('✅ 使用服务器数据');
                return {
                    orderData: chartData.orderData || this.defaultData.orderData,
                    overdueData: chartData.overdueData || this.defaultData.overdueData
                };
            }
            
            // 尝试Ajax获取数据
            console.log('🔄 尝试Ajax获取数据...');
            const response = await fetch('/api/chart-data');
            if (response.ok) {
                const data = await response.json();
                console.log('✅ Ajax获取数据成功');
                return data;
            }
            
            throw new Error('服务器数据不可用');
            
        } catch (error) {
            console.warn('⚠️ 服务器数据获取失败，使用默认数据:', error.message);
            return this.defaultData;
        }
    }
    
    /**
     * 处理初始化错误的企业级降级策略
     */
    async handleInitializationError(error) {
        console.warn('🔧 执行错误恢复策略...');
        
        try {
            // 使用默认数据重新初始化
            await this.createOrderChart(this.defaultData.orderData);
            await this.createOverdueChart(this.defaultData.overdueData);
            this.bindEventListeners();
            
            // 显示用户友好的提示
            this.showErrorNotification('图表数据加载失败，已显示演示数据');
            
            console.log('✅ 错误恢复成功，使用默认数据');
            
        } catch (recoveryError) {
            console.error('❌ 错误恢复失败:', recoveryError);
            this.showErrorNotification('图表初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 创建订单图表
     * @param {Object} data - 图表数据
     */
    async createOrderChart(data) {
        const config = {
            type: 'line',
            data: data,
            options: this.chartEngine.getStandardChartOptions('订单趋势图')
        };
        
        this.orderChart = await this.chartEngine.createChart('orderChart', config);
        console.log('✅ 订单图表创建完成');
    }
    
    /**
     * 创建逾期图表
     * @param {Object} data - 图表数据
     */
    async createOverdueChart(data) {
        const config = {
            type: 'bar',
            data: data,
            options: this.chartEngine.getStandardChartOptions('逾期订单统计图')
        };
        
        this.overdueChart = await this.chartEngine.createChart('overdueChart', config);
        console.log('✅ 逾期图表创建完成');
    }
    
    /**
     * 切换图表类型（公共接口）
     * @param {string} chartId - 图表ID
     * @param {string} newType - 新的图表类型
     */
    async switchChartType(chartId, newType) {
        try {
            const success = await this.chartEngine.switchChartType(chartId, newType);
            
            if (success) {
                // 更新UI状态
                this.updateChartTypeButtons(chartId, newType);
                console.log(`✅ 图表类型切换成功: ${chartId} → ${newType}`);
            } else {
                throw new Error('图表类型切换失败');
            }
            
        } catch (error) {
            console.error(`❌ 图表类型切换失败: ${chartId}`, error);
            this.showErrorNotification(`切换图表类型失败: ${error.message}`);
        }
    }
    
    /**
     * 更新图表类型按钮状态
     * @param {string} chartId - 图表ID
     * @param {string} activeType - 当前激活的类型
     */
    updateChartTypeButtons(chartId, activeType) {
        const buttonGroup = document.querySelector(`[data-chart="${chartId}"]`);
        if (!buttonGroup) return;
        
        // 移除所有按钮的激活状态
        buttonGroup.querySelectorAll('button').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        });
        
        // 激活当前类型按钮
        const activeButton = buttonGroup.querySelector(`[data-type="${activeType}"]`);
        if (activeButton) {
            activeButton.classList.remove('btn-outline-primary');
            activeButton.classList.add('btn-primary');
        }
    }
    
    /**
     * 刷新图表数据
     */
    async refreshCharts() {
        try {
            console.log('🔄 刷新图表数据...');
            
            // 显示加载状态
            this.showLoadingState(true);
            
            // 重新获取数据
            const serverData = await this.fetchServerData();
            
            // 更新图表数据
            if (this.orderChart) {
                this.orderChart.data = serverData.orderData;
                this.orderChart.update('active');
            }
            
            if (this.overdueChart) {
                this.overdueChart.data = serverData.overdueData;
                this.overdueChart.update('active');
            }
            
            console.log('✅ 图表数据刷新完成');
            this.showSuccessNotification('图表数据已更新');
            
        } catch (error) {
            console.error('❌ 图表数据刷新失败:', error);
            this.showErrorNotification('图表数据刷新失败');
        } finally {
            this.showLoadingState(false);
        }
    }
    
    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 图表类型切换按钮
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-chart-type]')) {
                const chartId = event.target.getAttribute('data-chart');
                const chartType = event.target.getAttribute('data-type');
                this.switchChartType(chartId, chartType);
            }
        });
        
        // 刷新按钮
        const refreshButton = document.getElementById('refreshCharts');
        if (refreshButton) {
            refreshButton.addEventListener('click', this.refreshCharts);
        }
        
        // 窗口大小变化处理
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
        
        console.log('✅ 事件监听器绑定完成');
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        if (this.orderChart) {
            this.orderChart.resize();
        }
        if (this.overdueChart) {
            this.overdueChart.resize();
        }
    }
    
    /**
     * 生成默认演示数据
     * @returns {Object} 默认数据结构
     */
    generateDefaultData() {
        const currentDate = new Date();
        const labels = [];
        
        // 生成过去12个月的标签
        for (let i = 11; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            labels.push(date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit' }));
        }
        
        // 生成模拟数据
        const orderNumbers = labels.map(() => Math.floor(Math.random() * 50) + 20);
        const overdueNumbers = labels.map(() => Math.floor(Math.random() * 15) + 2);
        
        return {
            orderData: {
                labels: labels,
                datasets: [
                    {
                        label: '正常订单',
                        data: orderNumbers,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.4
                    },
                    {
                        label: '逾期订单',
                        data: overdueNumbers,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.4
                    }
                ]
            },
            overdueData: {
                labels: labels.slice(-6), // 最近6个月
                datasets: [
                    {
                        label: '逾期订单数量',
                        data: overdueNumbers.slice(-6),
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }
                ]
            }
        };
    }
    
    /**
     * 显示加载状态
     * @param {boolean} show - 是否显示加载状态
     */
    showLoadingState(show) {
        const loadingElements = document.querySelectorAll('.chart-loading');
        loadingElements.forEach(element => {
            element.style.display = show ? 'block' : 'none';
        });
    }
    
    /**
     * 显示成功通知
     * @param {string} message - 通知消息
     */
    showSuccessNotification(message) {
        this.showNotification(message, 'success');
    }
    
    /**
     * 显示错误通知
     * @param {string} message - 错误消息
     */
    showErrorNotification(message) {
        this.showNotification(message, 'danger');
    }
    
    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, danger, warning, info)
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    /**
     * 销毁控制器（清理资源）
     */
    destroy() {
        if (this.orderChart) {
            this.chartEngine.destroyChart('orderChart');
            this.orderChart = null;
        }
        
        if (this.overdueChart) {
            this.chartEngine.destroyChart('overdueChart');
            this.overdueChart = null;
        }
        
        this.isInitialized = false;
        console.log('🗑️ 图表控制器已销毁');
    }
    
    /**
     * 获取控制器状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            hasOrderChart: !!this.orderChart,
            hasOverdueChart: !!this.overdueChart,
            engineStatus: this.chartEngine.getStatus()
        };
    }
}

// 导出到全局作用域
window.SummaryChartController = SummaryChartController;

// AMD/CommonJS兼容
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SummaryChartController;
} 