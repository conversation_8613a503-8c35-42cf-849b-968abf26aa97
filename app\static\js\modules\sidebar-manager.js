/**
 * 侧边栏管理模块
 * 负责侧边栏交互、功能注册和状态管理
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils || !window.TaixiangApp.Navigation) {
    throw new Error('依赖模块未加载，请先加载 app-core.js 和 navigation-manager.js');
}

// 侧边栏管理器类
TaixiangApp.SidebarManager = class {
    constructor() {
        this.sidebar = null;
        this.toggleButton = null;
        this.expandButton = null;
        this.isMobile = window.innerWidth <= 768;
        this.isCollapsed = false;
        this.initialized = false;
    }
    
    init() {
        if (this.initialized) {
            console.log('侧边栏管理器已初始化，跳过重复初始化');
            return;
        }
        
        console.log('初始化侧边栏管理器');
        
        // 获取DOM元素
        this.sidebar = document.querySelector('.sidebar');
        this.toggleButton = document.getElementById('collapseToggle');
        this.expandButton = document.getElementById('expandToggle');
        
        if (!this.sidebar) {
            console.warn('未找到侧边栏元素');
            return;
        }
        
        // 注册事件监听器
        this.registerEventListeners();
        
        // 恢复状态
        this.restoreState();
        
        // 处理窗口大小变化
        this.handleResize();
        
        this.initialized = true;
        console.log('侧边栏管理器初始化完成');
    }
    
    registerEventListeners() {
        // 折叠按钮点击事件
        if (this.toggleButton) {
            this.toggleButton.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        // 展开按钮点击事件
        if (this.expandButton) {
            this.expandButton.addEventListener('click', () => {
                this.expandSidebar();
            });
        }
        
        // 窗口大小变化事件
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 移动端遮罩点击事件
        const overlay = document.getElementById('sidebarOverlay');
        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }
        
        // 移动端切换按钮
        const mobileToggle = document.getElementById('sidebarToggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }
    }
    
    toggleSidebar() {
        if (this.isMobile) {
            this.toggleMobileSidebar();
        } else {
            if (this.isCollapsed) {
                this.expandSidebar();
            } else {
                this.collapseSidebar();
            }
        }
    }
    
    collapseSidebar() {
        if (this.isMobile || !this.sidebar) return;
        
        this.sidebar.classList.add('sidebar-collapsed');
        this.isCollapsed = true;
        this.saveState();
        
        // 更新按钮图标
        if (this.toggleButton) {
            const icon = this.toggleButton.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-chevron-right';
            }
        }
        
        console.log('侧边栏已折叠');
    }
    
    expandSidebar() {
        if (this.isMobile || !this.sidebar) return;
        
        this.sidebar.classList.remove('sidebar-collapsed');
        this.isCollapsed = false;
        this.saveState();
        
        // 更新按钮图标
        if (this.toggleButton) {
            const icon = this.toggleButton.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-chevron-left';
            }
        }
        
        console.log('侧边栏已展开');
    }
    
    toggleMobileSidebar() {
        if (!this.isMobile || !this.sidebar) return;
        
        this.sidebar.classList.toggle('sidebar-active');
        const overlay = document.getElementById('sidebarOverlay');
        if (overlay) {
            overlay.classList.toggle('overlay-active');
        }
    }
    
    closeMobileSidebar() {
        if (!this.isMobile || !this.sidebar) return;
        
        this.sidebar.classList.remove('sidebar-active');
        const overlay = document.getElementById('sidebarOverlay');
        if (overlay) {
            overlay.classList.remove('overlay-active');
        }
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        // 如果从移动端切换到桌面端，或反之
        if (wasMobile !== this.isMobile) {
            if (this.isMobile) {
                // 切换到移动端
                this.sidebar.classList.remove('sidebar-collapsed');
                this.closeMobileSidebar();
            } else {
                // 切换到桌面端
                this.sidebar.classList.remove('sidebar-active');
                const overlay = document.getElementById('sidebarOverlay');
                if (overlay) {
                    overlay.classList.remove('overlay-active');
                }
                this.restoreState();
            }
        }
    }
    
    saveState() {
        if (!this.isMobile && this.sidebar) {
            localStorage.setItem('sidebarCollapsed', this.isCollapsed);
        }
    }
    
    restoreState() {
        if (this.isMobile || !this.sidebar) return;
        
        const collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        
        if (collapsed) {
            this.collapseSidebar();
        } else {
            this.expandSidebar();
        }
    }
};

// 侧边栏功能管理器
TaixiangApp.SidebarFunctionManager = {
    // 初始化所有侧边栏功能
    init: function() {
        console.log('初始化侧边栏功能管理器');
        
        // 注册各种功能
        this.registerDateFilter();
        this.registerCustomerSearch();
        this.registerOverdueQuery();
        this.registerLogout();
        this.setupDatePickerIcon();
    },
    
    // 注册日期筛选功能
    registerDateFilter: function() {
        const dateFilterForm = document.getElementById('dateFilterForm');
        if (dateFilterForm) {
            dateFilterForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const date = document.getElementById('date').value;
                if (!date) {
                    alert('请选择日期');
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    TaixiangApp.Navigation.filterByDate(date);
                } else {
                    const url = new URL(window.location.origin + '/');
                    url.searchParams.set('date', date);
                    url.searchParams.set('tab', 'filter');
                    window.location.href = url.toString();
                }
            });
        }
    },
    
    // 注册客户搜索功能
    registerCustomerSearch: function() {
        const customerSearchForm = document.getElementById('customerSearchForm');
        if (customerSearchForm) {
            customerSearchForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const customerName = document.getElementById('customerName').value;
                if (!customerName) {
                    alert('请输入客户姓名');
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    TaixiangApp.Navigation.searchCustomer();
                } else {
                    const url = new URL(window.location.origin + '/');
                    url.searchParams.set('customerName', customerName);
                    url.searchParams.set('tab', 'customer');
                    window.location.href = url.toString();
                }
            });
        }
    },
    
    // 注册逾期订单查询功能
    registerOverdueQuery: function() {
        const overdueButton = document.getElementById('overdueButton');
        if (overdueButton) {
            overdueButton.addEventListener('click', function(event) {
                event.preventDefault();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    TaixiangApp.Navigation.showOverdueOrders();
                } else {
                    window.location.href = '/?tab=overdue';
                }
            });
        }
    },
    
    // 注册退出登录功能
    registerLogout: function() {
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
            logoutButton.addEventListener('click', function(event) {
                event.preventDefault();
                TaixiangApp.Navigation.logout();
            });
        }
    },
    
    // 设置日期选择器图标点击事件
    setupDatePickerIcon: function() {
        const calendarIcons = document.querySelectorAll('.calendar-icon');
        
        calendarIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const dateInput = this.parentElement.querySelector('input[type="date"]');
                if (dateInput) {
                    dateInput.focus();
                    dateInput.click();
                }
            });
        });
        
        const dateInputs = document.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(input => {
            input.addEventListener('change', function() {
                const container = this.closest('.date-input-container');
                if (container) {
                    const icon = container.querySelector('.calendar-icon');
                    if (icon) {
                        icon.style.opacity = '1';
                    }
                }
            });
        });
    }
};

// 向后兼容的全局对象
window.SidebarManager = TaixiangApp.SidebarManager;
window.SidebarFunctionManager = TaixiangApp.SidebarFunctionManager;

// 向后兼容的全局函数
window.setupDatePickerIcon = TaixiangApp.SidebarFunctionManager.setupDatePickerIcon.bind(TaixiangApp.SidebarFunctionManager);

console.log('侧边栏管理模块已加载');