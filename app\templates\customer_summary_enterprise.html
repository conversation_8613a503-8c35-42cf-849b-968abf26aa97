{% extends "base.html" %}

{% block title %}太享查询_{{ version }} - 客户订单汇总 - {{ customer_name }}{% endblock %}

{% block styles %}
<!-- 企业级客户汇总页面样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/customer-summary-enterprise.css') }}">
<!-- 移动端适配专用样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise/mobile-adaptive.css') }}">
{% endblock %}

{% block content %}
<div class="enterprise-customer-summary">
    <div class="container-fluid">
        <div class="row">
            <!-- 引入侧边栏模板 -->
            {% include 'sidebar.html' %}

            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 main-content">
                
                <!-- 页面标题区域 -->
                <div class="enterprise-page-header">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center">
                        <h1 class="enterprise-page-title">
                            <i class="bi bi-person-lines-fill"></i>
                            客户订单汇总: {{ customer_name }}
                        </h1>
                        <div class="enterprise-page-actions">
                            <a href="javascript:history.back()" class="enterprise-action-btn">
                                <i class="bi bi-arrow-left"></i>
                                返回
                            </a>
                            <a href="javascript:exportCharts()" class="enterprise-action-btn">
                                <i class="bi bi-download"></i>
                                导出图表
                            </a>
                            <a href="javascript:location.reload()" class="enterprise-action-btn">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新数据
                            </a>
                        </div>
                    </div>
                </div>

                {% if summary_data %}
                <!-- 客户基本信息卡片 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-person-badge"></i>
                            客户基本信息
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <div class="enterprise-customer-info">
                            <div>
                                <table class="enterprise-info-table">
                                    <tbody>
                                        <tr>
                                            <th>客户名称</th>
                                            <td>{{ customer_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系电话</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('phones', [])|join(', ') if summary_data.get('basic_info', {}).get('phones') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>设备型号</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('device_models', [])|join(', ') if summary_data.get('basic_info', {}).get('device_models') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>总台数</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('total_devices_count', 0) or 0 }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div>
                                <table class="enterprise-info-table">
                                    <tbody>
                                        <tr>
                                            <th>客服归属</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('services', [])|join(', ') if summary_data.get('basic_info', {}).get('services') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>业务归属</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('business_affiliations', [])|join(', ') if summary_data.get('basic_info', {}).get('business_affiliations') else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>客户备注</th>
                                            <td>{{ summary_data.get('basic_info', {}).get('customer_remarks', '-') or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>数据更新</th>
                                            <td>{{ moment().format('YYYY-MM-DD HH:mm') if moment else '实时数据' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单汇总统计卡片 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-graph-up-arrow"></i>
                            订单汇总统计
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <!-- 统计卡片网格 -->
                        <div class="enterprise-stats-grid">
                            <div class="enterprise-stat-card stat-primary">
                                <div class="enterprise-stat-title">总订单数</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('total_count', 0) or 0 }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-success">
                                <div class="enterprise-stat-title">总融资金额</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('total_amount', '¥0') or '¥0' }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-warning">
                                <div class="enterprise-stat-title">已还款金额</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('repaid_amount', '¥0') or '¥0' }}</div>
                            </div>
                            <div class="enterprise-stat-card stat-secondary">
                                <div class="enterprise-stat-title">当前待收</div>
                                <div class="enterprise-stat-value">{{ summary_data.get('order_summary', {}).get('current_receivable', '¥0') or '¥0' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据可视化图表 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-header">
                        <h5 class="enterprise-card-title">
                            <i class="bi bi-bar-chart-line"></i>
                            数据可视化分析
                        </h5>
                    </div>
                    <div class="enterprise-card-body">
                        <div class="enterprise-charts-grid">
                            <!-- 订单金额分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">订单金额分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="orderAmountChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- 待收金额分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">待收金额分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="receivableAmountChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- 业务类型分布图表 -->
                            <div class="enterprise-chart-container">
                                <div class="enterprise-chart-title">业务类型分布</div>
                                <div class="enterprise-chart-wrapper">
                                    <canvas id="businessTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格区域 -->
                <div class="enterprise-data-tabs">
                    <!-- 移动端专用标签页导航 -->
                    <div class="enterprise-tab-header">
                        <!-- 桌面端标签导航 -->
                        <ul class="nav nav-tabs enterprise-tab-nav d-none d-md-flex" id="customerTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="receivable-tab" data-bs-toggle="tab" 
                                        data-bs-target="#receivable" type="button" role="tab" 
                                        aria-controls="receivable" aria-selected="true">
                                    <i class="bi bi-currency-dollar"></i>
                                    <span class="tab-text">待收明细</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" 
                                        data-bs-target="#orders" type="button" role="tab" 
                                        aria-controls="orders" aria-selected="false">
                                    <i class="bi bi-list-ul"></i>
                                    <span class="tab-text">订单详情</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="finance-tab" data-bs-toggle="tab" 
                                        data-bs-target="#finance" type="button" role="tab" 
                                        aria-controls="finance" aria-selected="false">
                                    <i class="bi bi-receipt"></i>
                                    <span class="tab-text">财务流水</span>
                                </button>
                            </li>
                        </ul>

                        <!-- 移动端下拉选择器 -->
                        <div class="mobile-tab-selector d-md-none">
                            <div class="mobile-tab-category">数据视图选择</div>
                            <select class="form-select mobile-tab-select" id="mobileTabSelect">
                                <option value="receivable" selected>待收明细</option>
                                <option value="orders">订单详情</option>
                                <option value="finance">财务流水</option>
                            </select>
                            <div class="mobile-tab-indicator">
                                <div class="indicator-content">
                                    <div class="indicator-icon">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <span class="indicator-text">待收明细</span>
                                    <span class="indicator-badge">5项</span>
                                </div>
                                <i class="bi bi-chevron-down dropdown-arrow"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页内容 -->
                    <div class="tab-content enterprise-tab-content" id="customerTabsContent">
                        <!-- 待收明细 -->
                        <div class="tab-pane fade show active" id="receivable" role="tabpanel" 
                             aria-labelledby="receivable-tab">
                            
                            <!-- 桌面端表格视图 -->
                            <div class="desktop-table-view d-none d-lg-block">
                                <div class="table-responsive">
                                    <table class="table enterprise-data-table" id="receivableTable">
                                        <thead>
                                            <tr>
                                                <th class="dtr-control"></th>
                                                <th>待收期数</th>
                                                <th>订单数量</th>
                                                <th>台数</th>
                                                <th>待收金额</th>
                                                <th>逾期状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if summary_data.get('receivable_by_periods') %}
                                                {% for item in summary_data.get('receivable_by_periods', []) %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td>第{{ item.get('period', '-') }}期</td>
                                                    <td>{{ item.get('order_count', 0) }}</td>
                                                    <td>{{ item.get('devices_count', 0) }}</td>
                                                    <td data-type="amount">{{ item.get('amount', '0') }}</td>
                                                    <td data-type="status">
                                                        {% if item.get('overdue_days') and item.get('overdue_days') > 0 %}
                                                            逾期{{ item.get('overdue_days') }}天
                                                        {% else %}
                                                            正常
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td colspan="5" class="text-center text-muted">暂无待收明细数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                                            {% if summary_data.get('receivable_by_periods') %}
                                {% for item in summary_data.get('receivable_by_periods', []) %}
                                    <div class="mobile-data-card" data-category="receivable">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="bi bi-calendar-check"></i>
                                                第{{ item.period }}期
                                            </div>
                                            <div class="card-status">
                                                {% if item.overdue_days and item.overdue_days > 0 %}
                                                    <span class="status-badge status-overdue">
                                                        <i class="bi bi-exclamation-triangle"></i>
                                                        逾期{{ item.overdue_days }}天
                                                    </span>
                                                {% else %}
                                                    <span class="status-badge status-normal">
                                                        <i class="bi bi-check-circle"></i>
                                                        正常
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-data-grid">
                                                <div class="data-item">
                                                    <div class="data-label">订单数量</div>
                                                    <div class="data-value">{{ item.order_count }}</div>
                                                </div>
                                                <div class="data-item">
                                                    <div class="data-label">台数</div>
                                                    <div class="data-value">{{ item.devices_count }}</div>
                                                </div>
                                                <div class="data-item highlight">
                                                    <div class="data-label">待收金额</div>
                                                    <div class="data-value amount">{{ item.amount }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showDetails(this)">
                                                <i class="bi bi-eye"></i> 查看详情
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="expandCard(this)">
                                                <i class="bi bi-arrows-expand"></i> 展开
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-inbox"></i>
                                        <h5>暂无待收明细数据</h5>
                                        <p>当前没有可显示的待收明细信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 订单详情 -->
                        <div class="tab-pane fade" id="orders" role="tabpanel" aria-labelledby="orders-tab">
                            
                            <!-- 桌面端表格视图 -->
                            <div class="desktop-table-view d-none d-lg-block">
                                <div class="table-responsive">
                                    <table class="table enterprise-data-table" id="ordersTable">
                                        <thead>
                                            <tr>
                                                <th class="dtr-control"></th>
                                                <th>订单日期</th>
                                                <th>订单编号</th>
                                                <th>产品类型</th>
                                                <th>总融资额</th>
                                                <th>当前待收</th>
                                                <th>总期数</th>
                                                <th>待收期数</th>
                                                <th>台数</th>
                                                <th>业务类型</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if summary_data.get('order_details') %}
                                                {% for item in summary_data.get('order_details', []) %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td data-type="date">{{ item.get('order_date', '-') }}</td>
                                                    <td>{{ item.get('order_number', '-') }}</td>
                                                    <td data-type="status">{{ item.get('product_type', '-') }}</td>
                                                    <td data-type="amount">{{ item.get('total_finance', '-') }}</td>
                                                    <td data-type="amount">{{ item.get('current_receivable', '-') }}</td>
                                                    <td>{{ item.get('total_periods', '-') }}</td>
                                                    <td>{{ item.get('current_receivable_periods', '-') }}</td>
                                                    <td>{{ item.get('devices_count', '-') }}</td>
                                                    <td data-type="status">{{ item.get('business_type', '-') }}</td>
                                                </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td colspan="9" class="text-center text-muted">暂无订单详情数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                                            {% if summary_data.get('order_details') %}
                                {% for item in summary_data.get('order_details', []) %}
                                    <div class="mobile-data-card order-detail-card" data-category="orders">
                                        <div class="card-header">
                                            <div class="card-title-section">
                                                <div class="card-title">
                                                    <i class="bi bi-file-earmark-text"></i>
                                                    <span class="title-text">{{ item.get('order_number', '订单编号') }}</span>
                                                </div>
                                                <div class="card-date">
                                                    <i class="bi bi-calendar3"></i>
                                                    <span class="date-text">{{ item.get('order_date', '-') }}</span>
                                                </div>
                                            </div>
                                            <div class="card-status-section">
                                                <span class="status-badge status-business">
                                                    {{ item.get('business_type', '-') }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="order-summary-section">
                                                <div class="summary-item primary">
                                                    <div class="summary-label">总融资额</div>
                                                    <div class="summary-value amount-primary">{{ item.get('total_finance', '-') }}</div>
                                                </div>
                                                <div class="summary-item secondary">
                                                    <div class="summary-label">当前待收</div>
                                                    <div class="summary-value amount-secondary">{{ item.get('current_receivable', '-') }}</div>
                                                </div>
                                            </div>
                                            
                                            <div class="order-details-section">
                                                <div class="detail-group">
                                                    <div class="detail-row">
                                                        <div class="detail-item">
                                                            <div class="detail-label">产品类型</div>
                                                            <div class="detail-value">{{ item.get('product_type', '-') }}</div>
                                                        </div>
                                                        <div class="detail-item">
                                                            <div class="detail-label">设备台数</div>
                                                            <div class="detail-value">{{ item.get('devices_count', '-') }} 台</div>
                                                        </div>
                                                    </div>
                                                    <div class="detail-row">
                                                        <div class="detail-item">
                                                            <div class="detail-label">总期数</div>
                                                            <div class="detail-value">{{ item.get('total_periods', '-') }} 期</div>
                                                        </div>
                                                        <div class="detail-item">
                                                            <div class="detail-label">剩余期数</div>
                                                            <div class="detail-value">{{ item.get('current_receivable_periods', '-') }} 期</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showOrderDetails(this)">
                                                <i class="bi bi-info-circle"></i> 详细信息
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="showPaymentHistory(this)">
                                                <i class="bi bi-clock-history"></i> 还款记录
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-file-earmark-x"></i>
                                        <h5>暂无订单详情数据</h5>
                                        <p>当前没有可显示的订单详情信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 财务流水 -->
                        <div class="tab-pane fade" id="finance" role="tabpanel" aria-labelledby="finance-tab">
                            
                            <!-- 桌面端表格视图 -->
                            <div class="desktop-table-view d-none d-lg-block">
                                <div class="table-responsive">
                                    <table class="table enterprise-data-table" id="financeTable">
                                        <thead>
                                            <tr>
                                                <th class="dtr-control"></th>
                                                <th>日期</th>
                                                <th>交易流水号</th>
                                                <th>交易类型</th>
                                                <th>金额</th>
                                                <th>资金流向</th>
                                                <th>订单编号</th>
                                                <th>备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if summary_data.get('finance_records') %}
                                                {% for item in summary_data.get('finance_records', []) %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td data-type="date">{{ item.transaction_date }}</td>
                                                    <td>{{ item.transaction_id }}</td>
                                                    <td data-type="status">{{ item.transaction_type }}</td>
                                                    <td data-type="amount">{{ item.amount }}</td>
                                                    <td data-type="status">{{ item.flow_direction }}</td>
                                                    <td>{{ item.order_number }}</td>
                                                    <td>{{ item.get('remarks', '-') }}</td>
                                                </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td class="dtr-control"></td>
                                                    <td colspan="7" class="text-center text-muted">暂无财务流水数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 移动端卡片视图 -->
                            <div class="mobile-card-view d-lg-none">
                                {% if summary_data.get('finance_records') %}
                                    {% for item in summary_data.get('finance_records', []) %}
                                    <div class="mobile-data-card" data-category="finance">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="bi bi-credit-card"></i>
                                                {{ item.transaction_type }}
                                            </div>
                                            <div class="card-meta">
                                                <span class="meta-date">{{ item.transaction_date }}</span>
                                                <span class="meta-flow {{ 'flow-in' if '收入' in item.flow_direction else 'flow-out' }}">
                                                    <i class="bi {{ 'bi-arrow-down-circle' if '收入' in item.flow_direction else 'bi-arrow-up-circle' }}"></i>
                                                    {{ item.flow_direction }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="card-main-info">
                                                <div class="main-amount {{ 'amount-positive' if '收入' in item.flow_direction else 'amount-negative' }}">
                                                    <span class="amount-symbol">{{ '+' if '收入' in item.flow_direction else '-' }}</span>
                                                    <span class="amount-value">{{ item.amount }}</span>
                                                </div>
                                                <div class="main-reference">
                                                    <span class="reference-label">关联订单</span>
                                                    <span class="reference-value">{{ item.order_number }}</span>
                                                </div>
                                            </div>
                                            <div class="card-transaction-details">
                                                <div class="transaction-id">
                                                    <span class="detail-label">交易流水号</span>
                                                    <span class="detail-value">{{ item.transaction_id }}</span>
                                                </div>
                                                {% if item.get('remarks') and item.get('remarks') != '-' %}
                                                <div class="transaction-remarks">
                                                    <span class="detail-label">备注</span>
                                                    <span class="detail-value">{{ item.remarks }}</span>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-info" onclick="showTransactionDetails(this)">
                                                <i class="bi bi-receipt"></i> 交易详情
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="copyTransactionId(this)">
                                                <i class="bi bi-clipboard"></i> 复制流水号
                                            </button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="mobile-empty-state">
                                        <i class="bi bi-journal-x"></i>
                                        <h5>暂无财务流水数据</h5>
                                        <p>当前没有可显示的财务流水信息</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <!-- 无数据状态 -->
                <div class="enterprise-card">
                    <div class="enterprise-card-body">
                        <div class="d-flex align-items-center justify-content-center py-5">
                            <div class="text-center">
                                <i class="bi bi-inbox" style="font-size: 4rem; color: #cbd5e1; margin-bottom: 1rem;"></i>
                                <h4 class="text-muted">暂无客户数据</h4>
                                <p class="text-muted">无法获取客户订单汇总数据，请检查客户信息或稍后再试。</p>
                                <button class="btn btn-primary" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    重新加载
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的图表数据元素 -->
{% if chart_data %}
<script id="chart-data" type="application/json">{{ chart_data|tojson|safe }}</script>
{% endif %}
{% endblock %}

{% block scripts %}
<!-- 企业级客户汇总页面脚本 -->
<script src="{{ url_for('static', filename='js/enterprise/customer-summary-enterprise.js') }}"></script>
<!-- 移动端适配专用脚本 -->
<script src="{{ url_for('static', filename='js/enterprise/mobile-adaptive.js') }}"></script>

<!-- 页面初始化脚本 -->
<script>
// 设置全局图表数据
{% if chart_data %}
window.chartData = {{ chart_data|tojson|safe }};
{% else %}
window.chartData = null;
{% endif %}

// 提供页面级别的导出功能
window.exportCharts = function() {
    if (window.enterpriseCustomerSummaryManager) {
        window.enterpriseCustomerSummaryManager.setupChartExport();
        const event = new Event('click');
        window.exportCharts();
    } else {
        console.warn('客户汇总管理器未初始化');
        alert('导出功能暂时不可用，请稍后再试');
    }
};

// 提供页面级别的数据刷新功能
window.refreshCustomerData = function() {
    location.reload();
};

// 错误处理和降级方案
window.addEventListener('error', function(e) {
    console.error('页面发生错误:', e.error);
    
    // 简单的错误恢复
    if (e.error.message && e.error.message.includes('Chart')) {
        console.log('图表相关错误，尝试使用后备方案');
        const chartContainers = document.querySelectorAll('.enterprise-chart-wrapper');
        chartContainers.forEach(container => {
            if (!container.querySelector('canvas')) {
                container.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                        <div class="text-center">
                            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                            <p class="mt-2 mb-0">图表加载失败</p>
                            <small>请刷新页面重试</small>
                        </div>
                    </div>
                `;
            }
        });
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && window.enterpriseCustomerSummaryManager) {
        // 页面重新可见时，刷新图表和表格
        setTimeout(() => {
            window.enterpriseCustomerSummaryManager.resizeCharts();
            window.enterpriseCustomerSummaryManager.refreshAllTables();
        }, 100);
    }
});

// 打印支持
window.addEventListener('beforeprint', function() {
    // 打印前的准备工作
    const charts = document.querySelectorAll('canvas');
    charts.forEach(canvas => {
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';
    });
});

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+R 或 F5: 刷新数据
    if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
        e.preventDefault();
        location.reload();
    }
    
    // Ctrl+E: 导出图表
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        if (typeof exportCharts === 'function') {
            exportCharts();
        }
    }
    
    // Ctrl+B: 返回上一页
    if (e.ctrlKey && e.key === 'b') {
        e.preventDefault();
        history.back();
    }
});

console.log('企业级客户汇总页面脚本加载完成');
</script>
{% endblock %}