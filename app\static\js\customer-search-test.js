/**
 * 客户搜索功能测试工具
 * 用于验证模块化重构后的客户搜索功能是否正常工作
 */

window.CustomerSearchTest = {
    // 运行完整测试
    runFullTest: function() {
        console.log('🔍 开始客户搜索功能测试');
        console.log('===============================');
        
        const results = {
            moduleCheck: this.testModuleLoading(),
            domCheck: this.testDOMElements(),
            functionCheck: this.testFunctions(),
            eventCheck: this.testEventHandlers()
        };
        
        this.generateReport(results);
        return results;
    },
    
    // 测试模块加载
    testModuleLoading: function() {
        console.log('📦 测试模块加载状态...');
        
        const tests = {
            coreModule: !!window.TaixiangApp,
            navigationModule: !!window.TaixiangApp?.Navigation,
            quickSearchModule: !!window.TaixiangApp?.QuickSearch,
            customerSearchModule: !!window.TaixiangApp?.CustomerSearch,
            backwardCompatibility: !!window.AppNavigation,
            apiManager: !!window.TaixiangApp?.ApiDataManager
        };
        
        Object.keys(tests).forEach(test => {
            const status = tests[test] ? '✅' : '❌';
            console.log(`   ${test}: ${status}`);
        });
        
        return tests;
    },
    
    // 测试DOM元素
    testDOMElements: function() {
        console.log('🏷️ 测试DOM元素存在性...');
        
        const elements = {
            customerNameInput: document.getElementById('customerName'),
            quickSearchInput: document.getElementById('quickSearch'),
            customerSearchForm: document.getElementById('customerSearchForm'),
            quickSearchForm: document.getElementById('quickSearchForm')
        };
        
        const tests = {};
        Object.keys(elements).forEach(key => {
            tests[key] = !!elements[key];
            const status = tests[key] ? '✅' : '❌';
            console.log(`   ${key}: ${status}`);
        });
        
        return tests;
    },
    
    // 测试函数可用性
    testFunctions: function() {
        console.log('⚙️ 测试函数可用性...');
        
        const tests = {
            searchCustomer: !!(window.AppNavigation?.searchCustomer),
            quickSearchHandler: !!(window.TaixiangApp?.QuickSearch?.handleQuickSearch),
            navigationGoToHome: !!(window.TaixiangApp?.Navigation?.goToHome),
            filterByDate: !!(window.TaixiangApp?.Navigation?.filterByDate)
        };
        
        Object.keys(tests).forEach(test => {
            const status = tests[test] ? '✅' : '❌';
            console.log(`   ${test}: ${status}`);
        });
        
        return tests;
    },
    
    // 测试事件处理器
    testEventHandlers: function() {
        console.log('🎯 测试事件处理器...');
        
        const customerForm = document.getElementById('customerSearchForm');
        const quickForm = document.getElementById('quickSearchForm');
        
        const tests = {
            customerFormOnSubmit: !!(customerForm?.getAttribute('onsubmit')),
            quickFormExists: !!quickForm,
            customerFormExists: !!customerForm
        };
        
        Object.keys(tests).forEach(test => {
            const status = tests[test] ? '✅' : '❌';
            console.log(`   ${test}: ${status}`);
        });
        
        return tests;
    },
    
    // 生成测试报告
    generateReport: function(results) {
        console.log('===============================');
        console.log('📊 测试报告汇总');
        console.log('===============================');
        
        let totalTests = 0;
        let passedTests = 0;
        
        Object.keys(results).forEach(category => {
            const categoryResults = results[category];
            const categoryTotal = Object.keys(categoryResults).length;
            const categoryPassed = Object.values(categoryResults).filter(Boolean).length;
            
            totalTests += categoryTotal;
            passedTests += categoryPassed;
            
            const categoryStatus = categoryPassed === categoryTotal ? '✅' : '⚠️';
            console.log(`${categoryStatus} ${category}: ${categoryPassed}/${categoryTotal}`);
        });
        
        console.log('===============================');
        const overallStatus = passedTests === totalTests ? '🎉' : '⚠️';
        console.log(`${overallStatus} 总体状态: ${passedTests}/${totalTests} 测试通过`);
        
        if (passedTests === totalTests) {
            console.log('✅ 所有测试通过！客户搜索功能应该正常工作。');
        } else {
            console.log('⚠️ 部分测试失败，请检查上述问题。');
            this.provideTroubleshootingTips();
        }
    },
    
    // 提供故障排除建议
    provideTroubleshootingTips: function() {
        console.log('===============================');
        console.log('🔧 故障排除建议');
        console.log('===============================');
        console.log('1. 检查浏览器控制台是否有JavaScript错误');
        console.log('2. 确认所有模块文件已正确加载');
        console.log('3. 验证模块加载顺序是否正确');
        console.log('4. 检查DOM元素ID是否匹配');
        console.log('5. 确认事件监听器已正确绑定');
    },
    
    // 手动测试侧边栏搜索
    testSidebarSearch: function(testValue = '测试客户') {
        console.log('🧪 手动测试侧边栏搜索...');
        
        const input = document.getElementById('customerName');
        if (!input) {
            console.error('❌ 找不到customerName输入框');
            return false;
        }
        
        input.value = testValue;
        
        if (window.AppNavigation?.searchCustomer) {
            console.log('✅ 调用 AppNavigation.searchCustomer()');
            window.AppNavigation.searchCustomer();
            return true;
        } else {
            console.error('❌ AppNavigation.searchCustomer 不可用');
            return false;
        }
    },
    
    // 手动测试快速搜索
    testQuickSearch: function(testValue = '测试客户') {
        console.log('🧪 手动测试快速搜索...');
        
        const input = document.getElementById('quickSearch');
        if (!input) {
            console.error('❌ 找不到quickSearch输入框');
            return false;
        }
        
        input.value = testValue;
        
        if (window.TaixiangApp?.QuickSearch?.handleQuickSearch) {
            console.log('✅ 调用 TaixiangApp.QuickSearch.handleQuickSearch()');
            window.TaixiangApp.QuickSearch.handleQuickSearch();
            return true;
        } else {
            console.error('❌ TaixiangApp.QuickSearch.handleQuickSearch 不可用');
            return false;
        }
    },
    
    // 显示使用说明
    showUsage: function() {
        console.log('===============================');
        console.log('📖 客户搜索测试工具使用说明');
        console.log('===============================');
        console.log('CustomerSearchTest.runFullTest()          - 运行完整测试');
        console.log('CustomerSearchTest.testSidebarSearch()    - 测试侧边栏搜索');
        console.log('CustomerSearchTest.testQuickSearch()      - 测试快速搜索');
        console.log('CustomerSearchTest.showUsage()            - 显示使用说明');
        console.log('===============================');
        console.log('示例：');
        console.log('CustomerSearchTest.testSidebarSearch("张三")');
        console.log('CustomerSearchTest.testQuickSearch("李四")');
    }
};

// 页面加载完成后自动提供使用说明
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🔍 客户搜索测试工具已加载');
        console.log('在控制台输入 CustomerSearchTest.runFullTest() 开始测试');
    }, 1000);
});

console.log('客户搜索测试工具已加载'); 