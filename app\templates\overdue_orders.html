{% extends "base.html" %}

{% block title %}逾期订单查询_{{ version }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* 表格样式优化 */
    .data-table {
        width: 100% !important;
        font-size: 0.9rem;
    }
    
    /* 确保表格容器正确计算宽度 */
    .dataTables_wrapper {
        width: 100% !important;
        margin: 0 auto;
    }
    
    /* 表头和内容单元格样式 */
    .dataTable > thead > tr > th,
    .dataTable > tbody > tr > td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle !important;
    }
    
    /* 确保表格标题单元格匹配内容宽度 */
    table.dataTable thead th {
        position: relative;
    }
    
    /* 修复排序图标位置 */
    table.dataTable thead .sorting:before,
    table.dataTable thead .sorting_asc:before,
    table.dataTable thead .sorting_desc:before,
    table.dataTable thead .sorting_asc_disabled:before,
    table.dataTable thead .sorting_desc_disabled:before {
        bottom: 0.5em;
    }
    
    table.dataTable thead .sorting:after,
    table.dataTable thead .sorting_asc:after,
    table.dataTable thead .sorting_desc:after,
    table.dataTable thead .sorting_asc_disabled:after,
    table.dataTable thead .sorting_desc_disabled:after {
        top: 0.5em;
    }
    
    /* 防止文本换行 */
    .text-nowrap {
        white-space: nowrap !important;
    }
    
    /* 提高列宽度，避免文本换行 */
    .data-table th, .data-table td {
        padding: 8px 12px;
        vertical-align: middle !important;
    }
    
    /* 下拉选择器和控制区样式 */
    .table-controls-row {
        margin-bottom: 15px;
    }
    
    #pageSizeSelect {
        min-width: 80px;
        display: inline-block;
    }
    
    #tableSearch {
        max-width: 250px;
    }
    
    /* 特定字段样式 */
    .data-table th:nth-child(2), .data-table td:nth-child(2) {
        min-width: 150px; /* 订单编号列加宽 */
    }
    
    /* 金额字段样式 */
    .amount-field {
        font-weight: bold;
        text-align: right !important;
        color: #007bff;
    }
    
    /* 分类字段标签样式 */
    .category-field .badge {
        font-size: 0.85em;
        font-weight: normal;
    }
    
    /* 产品类型字段样式 - 区分不同产品类型 */
    .category-field .badge[data-product-type="电商"] {
        background-color: #cfe2ff !important;
        color: #084298 !important;
        border: 1px solid #b6d4fe;
    }
    
    .category-field .badge[data-product-type="租赁"] {
        background-color: #d1e7dd !important;
        color: #0f5132 !important;
        border: 1px solid #badbcc;
    }
    
    /* 日期字段标签样式 */
    .date-field .badge {
        font-size: 0.85em;
        font-weight: normal;
    }
    
    /* 状态字段标签样式 */
    .status-field .badge {
        font-size: 0.9em;
    }
    
    /* 表头的样式 */
    .data-table thead th {
        background-color: #f8f9fa;
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid #dee2e6;
    }
    
    /* 奇偶行颜色区分更明显 */
    .data-table tbody tr:nth-of-type(odd) {
        background-color: rgba(0,0,0,.02);
    }
    
    .data-table tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
    
    /* 分页控件样式 */
    .pagination-container {
        margin-top: 20px;
        margin-bottom: 30px;
    }
    
    /* 紧凑型表格控件样式 */
    .compact-table-controls .dataTables_wrapper .dataTables_length,
    .compact-table-controls .dataTables_wrapper .dataTables_filter {
        float: none;
        display: inline-block;
        vertical-align: middle;
        text-align: left;
    }
    
    .compact-table-controls .dataTables_wrapper .dataTables_length {
        margin-right: 15px;
    }
    
    .compact-table-controls .dataTables_wrapper .dataTables_filter {
        text-align: right;
    }
    
    .compact-table-controls .dataTables_wrapper .dataTable-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: nowrap;
        padding: 8px 0;
    }
    
    .compact-table-controls .dataTables_wrapper .dataTables_length select {
        width: auto;
        display: inline-block;
        margin: 0 0.25rem;
        padding-right: 25px;
    }
    
    .compact-table-controls .dataTables_wrapper .dataTables_filter input {
        width: 150px !important;
        margin-left: 0.5rem;
    }
    
    /* DataTable控件更紧凑布局 */
    .dataTable-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem 0;
    }
    
    .dataTables_length {
        margin-right: 1rem;
        white-space: nowrap;
    }
    
    .dataTables_length select {
        width: auto;
        display: inline-block;
        margin: 0 0.25rem;
    }
    
    .dataTables_filter {
        display: flex;
        align-items: center;
        margin-left: auto;
    }
    
    .dataTables_filter label {
        display: flex;
        align-items: center;
        margin-bottom: 0;
        white-space: nowrap;
    }
    
    .dataTables_filter input {
        width: auto !important;
        display: inline-block;
        margin-left: 0.5rem;
    }
    
    /* 改进移动设备上的响应式表现 */
    @media (max-width: 768px) {
        .data-table {
            font-size: 0.85rem;
        }
        
        .data-table th, .data-table td {
            padding: 6px 8px;
        }
        
        .amount-field {
            font-size: 0.9em;
        }
        
        .pagination-container .pagination {
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .pagination-container .page-item {
            margin-bottom: 5px;
        }
        
        /* 在移动设备上保持紧凑布局 */
        .dataTable-top {
            flex-wrap: wrap;
        }
        
        .dataTables_length, .dataTables_filter {
            margin: 0.25rem 0;
        }
        
        /* 下拉选择器样式优化 */
        #pageSizeSelect {
            min-width: 80px;
        }
        
        /* 页面控制区响应式调整 */
        .row.mb-3 .col-md-6:last-child {
            margin-top: 10px;
        }
        
        #pageSizeSelect {
            width: 80px !important;
        }
        
        .d-flex.align-items-center.justify-content-md-end {
            justify-content: flex-start !important; 
        }
    }
</style>
{% endblock %}


{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">逾期订单查询</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <!-- 刷新按钮 -->
                    <a href="{{ url_for('main.query.overdue_orders', refresh=1) }}" class="btn btn-sm btn-outline-primary" id="refreshData">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="ExportManager.exportData('excel', {dataSource: 'overdueOrders'})">
                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="ExportManager.exportData('csv', {dataSource: 'overdueOrders'})">
                        <i class="bi bi-file-earmark-text"></i> 导出CSV
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 缓存信息提示 -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i>
            数据最后更新时间: <strong>{{ last_update }}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
        </div>

        <!-- 加载状态更新区 -->
        <div id="loadingStatus" class="mb-2"></div>
        
        <!-- 表格控制区：搜索框与分页大小选择器 -->
        <div class="row mb-3 align-items-center table-controls-row">
            <div class="col-md-6">
                <form id="searchForm" action="{{ url_for('main.query.overdue_orders') }}" method="GET">
                    <!-- 保留当前的limit参数 -->
                    <input type="hidden" name="limit" value="{{ page_limit }}">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" id="tableSearch" name="search" class="form-control" placeholder="全局搜索..." aria-label="搜索" value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit">搜索</button>
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end">
                    <label for="pageSizeSelect" class="me-2 text-nowrap">每页显示:</label>
                    <select id="pageSizeSelect" class="form-select form-select-sm" style="width: auto;">
                        <option value="10" {% if page_limit == 10 %}selected{% endif %}>10 条</option>
                        <option value="25" {% if page_limit == 25 %}selected{% endif %}>25 条</option>
                        <option value="50" {% if page_limit == 50 %}selected{% endif %}>50 条</option>
                        <option value="100" {% if page_limit == 100 %}selected{% endif %}>100 条</option>
                        <option value="200" {% if page_limit == 200 %}selected{% endif %}>200 条</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 逾期订单内容 -->
        <div class="overdue-content">
            {% if overdue_results and overdue_results.results and overdue_results.results|length > 0 %}
                <div class="pt-3">
                    <div class="alert alert-success auto-dismiss-alert">
                        {% if search_query %}
                            搜索"{{ search_query }}"：共找到 {{ total_records }} 条匹配记录
                        {% else %}
                            共找到 {{ overdue_results.results|length }} 条逾期记录
                        {% endif %}
                    </div>
                </div>
                <div class="table-responsive compact-table-controls">
                    <table class="table table-striped table-hover data-table" style="width:100%" data-type="overdue" id="overdueTable">
                        <thead>
                            <tr>
                                <th></th> <!-- 用于响应式详情控制 -->
                                {% for column in overdue_results.columns %}
                                <th>{{ column }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {%- for row in overdue_results.results -%}
                            <tr {%- if '备注' in row %} data-status="{{ row['备注'] }}" {%- endif %}>
                                <td class="dtr-control"></td> <!-- 用于响应式详情控制 -->
                                {% for column in overdue_results.columns %}
                                <td {% if column == '备注' %}class="{{ row.get(column, '') }}"{% endif %}
                                   {% if column in ['订单编号', '客户手机', '客户姓名'] %}style="white-space: nowrap; min-width: 120px;"{% endif %}
                                   {% if column in ['当前待收', '总待收', '成本'] %}class="amount-field"{% endif %}
                                   {% if column in ['业务', '客服', '产品', '产品类型'] %}class="category-field"{% endif %}
                                   {% if column in ['订单日期', '账单日期', '首次逾期日期'] %}class="date-field"{% endif %}
                                   {% if column in ['逾期天数', '逾期期数', '首次逾期期数'] %}class="status-field"{% endif %}>
                                    {% if column in row %}
                                        {% if column in ['当前待收', '总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                            {{ "%.2f"|format(row[column]|float) }}
                                        {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                            <span class="badge bg-danger">{{ row[column] }}</span>
                                        {% elif column in ['业务', '客服'] %}
                                            <span class="badge bg-primary">{{ row[column] }}</span>
                                        {% elif column in ['产品', '产品类型'] %}
                                            {% if '电商' in row[column] %}
                                                <span class="badge bg-info text-dark" data-product-type="电商">{{ row[column] }}</span>
                                            {% elif '租赁' in row[column] %}
                                                <span class="badge bg-info text-dark" data-product-type="租赁">{{ row[column] }}</span>
                                            {% else %}
                                                <span class="badge bg-info text-dark">{{ row[column] }}</span>
                                            {% endif %}
                                        {% elif column in ['订单日期', '账单日期', '首次逾期日期'] %}
                                            <span class="badge bg-secondary">{{ row[column] }}</span>
                                        {% else %}
                                            {{ row[column] }}
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 优化后的分页控件 -->
                <div class="pagination-container mt-3">
                    <nav aria-label="逾期订单分页">
                        <ul class="pagination justify-content-center">
                            <!-- 上一页按钮 -->
                            <li class="page-item {% if current_page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=current_page-1, limit=page_limit, search=search_query) if current_page > 1 else '#' }}">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </a>
                            </li>
                            
                            <!-- 页码导航，使用数据验证的总页数 -->
                            {% if total_pages > 1 %}
                                <!-- 智能分页控件，显示当前页附近的页码 -->
                                {% set start_page = [1, current_page - 2]|max %}
                                {% set end_page = [total_pages, current_page + 2]|min %}
                                
                                <!-- 确保至少显示5个页码 -->
                                {% if end_page - start_page < 4 %}
                                    {% if current_page < total_pages - 2 %}
                                        {% set start_page = [1, end_page - 4]|max %}
                                    {% else %}
                                        {% set start_page = [1, total_pages - 4]|max %}
                                    {% endif %}
                                {% endif %}
                                
                                <!-- 显示第一页和省略号 -->
                                {% if start_page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=1, limit=page_limit, search=search_query) }}">1</a>
                                    </li>
                                    {% if start_page > 2 %}
                                        <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                    {% endif %}
                                {% endif %}
                                
                                <!-- 页码列表 -->
                                {% for p in range(start_page, end_page + 1) %}
                                    <li class="page-item {% if p == current_page %}active{% endif %}">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=p, limit=page_limit, search=search_query) }}">{{ p }}</a>
                                    </li>
                                {% endfor %}
                                
                                <!-- 显示最后一页和省略号 -->
                                {% if end_page < total_pages %}
                                    {% if end_page < total_pages - 1 %}
                                        <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                    {% endif %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=total_pages, limit=page_limit, search=search_query) }}">{{ total_pages }}</a>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            {% endif %}
                            
                            <!-- 下一页按钮 -->
                            <li class="page-item {% if current_page >= total_pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.query.overdue_orders', page=current_page+1, limit=page_limit, search=search_query) if current_page < total_pages else '#' }}">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    
                    <!-- 移除旧的页面大小选择器，改在顶部显示 -->
                    
                    <!-- 数据信息提示 -->
                    <div class="mt-2 text-center text-muted small">
                        {% if total_records > 0 %}
                            当前显示 {{ (current_page-1) * page_limit + 1 }} - {{ (current_page-1) * page_limit + overdue_results.results|length }} 条，共 {{ total_records }} 条记录
                        {% else %}
                            没有记录
                        {% endif %}
                    </div>
                </div>
                
                <!-- 注释掉DataTables自带的分页控件，由JavaScript动态处理 -->
                <script>
                    // 在DOM加载完成后执行
                    document.addEventListener('DOMContentLoaded', function() {
                        // 隐藏DataTables自带的分页控件
                        setTimeout(function() {
                            var dtPagination = document.querySelector('.dataTables_paginate');
                            if (dtPagination) {
                                dtPagination.style.display = 'none';
                            }
                            
                            var dtInfo = document.querySelector('.dataTables_info');
                            if (dtInfo) {
                                dtInfo.style.display = 'none';
                            }
                        }, 100);
                    });
                </script>
                
            {% elif overdue_results and 'error' in overdue_results %}
                <div class="alert alert-danger mt-3">
                    获取逾期数据失败: {{ overdue_results.error }}
                </div>
            {% else %}
                <div class="alert alert-info mt-3">
                    点击侧边栏的"逾期订单查询"按钮获取逾期订单数据。
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入SheetJS库用于Excel导出（本地文件） -->
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>

<!-- 将后端数据存储到全局变量中，供前端JavaScript使用 -->
<script type="text/javascript">
// 初始化全局变量
window.overdueResults = {};

// 除掉模板部分，初始化数据
{% if overdue_results %}
    // 从后端接收数据 - 预处理为JavaScript字符串
    window.overdueResults = {{ overdue_results|tojson|safe }};
    // 记录接收到的数据信息
    if (window.overdueResults && window.overdueResults.results) {
        console.log('接收到逾期数据: ' + window.overdueResults.results.length + ' 条记录');
    }
{% endif %}

// 在页面加载完成后，为导出管理器添加自定义逾期订单处理逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 确保ExportManager存在
    if (window.ExportManager) {
        // 为ExportManager添加逾期订单数据源处理器
        ExportManager.addDataSourceHandler('overdueOrders', function(format, options = {}) {
            // 获取当前搜索查询参数（如果有）
            const urlParams = new URLSearchParams(window.location.search);
            const searchQuery = urlParams.get('search') || '';
            
            // 直接检查window.overdueResults
            if (!window.overdueResults || !window.overdueResults.results || window.overdueResults.results.length === 0) {
                alert('没有可导出的数据');
                return Promise.reject(new Error('没有可导出的数据'));
            }
            
            // 获取分页信息
            const pagination = window.overdueResults.pagination || {};
            const totalRecords = pagination.total || 0;
            
            // 构建文件名
            const fileName = `逾期订单_${searchQuery ? '筛选数据_' : '全部数据_'}${new Date().toISOString().slice(0, 10)}`;
            
            // 使用统一的确认和加载状态显示
            return ExportManager.confirmExport(totalRecords)
                .then(confirmed => {
                    if (!confirmed) return Promise.reject(new Error('用户取消导出'));
                    
                    // 显示加载状态
                    if (window.LoadingController) {
                        LoadingController.showLoading('正在准备导出数据...');
                    }
                    
                    // 使用服务器端导出功能获取全部数据，并传递搜索查询
                    return fetch(`/api/export_all_overdue?format=${format}&search=${encodeURIComponent(searchQuery)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('服务器响应错误: ' + response.status);
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            return {
                                fileName: fileName,
                                data: blob,
                                totalRecords: totalRecords,
                                searchQuery: searchQuery
                            };
                        });
                });
        });
    }
});

// 如果URL中包含分页参数，记录下来
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');
    const limit = urlParams.get('limit');
    
    if (page) {
        console.log('当前页码: ' + page);
    }
    
    if (limit) {
        console.log('每页记录数: ' + limit);
    }
    
    // 初始化数据表格
    const dataTable = initializeOverdueDataTable();
});

// 初始化逾期订单数据表格
function initializeOverdueDataTable() {
    const tableElement = document.getElementById('overdueTable');
    if (!tableElement) return;
    
    // 显示加载提示
    const loadingStatus = document.getElementById('loadingStatus');
    if (loadingStatus) {
        loadingStatus.innerHTML = '<div class="alert alert-info">正在初始化表格，请稍候...</div>';
    }
    
    // 检查表格是否已初始化
    if ($.fn.DataTable.isDataTable(tableElement)) {
        console.log('逾期订单表格已初始化，销毁旧实例');
        $(tableElement).DataTable().destroy();
    }
    
    // 初始化DataTable
    const dataTable = $(tableElement).DataTable({
        responsive: {
            details: {
                type: 'column',
                target: 0 // 使用第一列作为控制列
            }
        },
        // 禁用自动列宽计算
        autoWidth: false,
        // 启用水平滚动
        scrollX: true,
        // 启用列宽固定
        fixedColumns: true,
        // 启用滚动条折叠
        scrollCollapse: true,
        columnDefs: [
            {
                className: 'dtr-control',
                orderable: false,
                targets: 0,
                width: "40px"
            },
            {
                targets: '_all',
                className: 'dt-head-center dt-body-center',
                // 让内容决定列宽
                width: null,
                render: function(data, type, row) {
                    // 确保数据正确渲染并防止换行
                    if (type === 'display') {
                        const content = data === null || data === undefined ? '-' : data;
                        return '<div class="text-nowrap">' + content + '</div>';
                    }
                    return data;
                }
            },
            // 对特定列进行优化
            {
                targets: [1, 2, 3], // 订单编号、客户姓名、日期等前几列
                className: 'dt-head-center dt-body-left',
                width: '120px' // 设置固定宽度
            }
        ],
        order: [[1, 'desc']],
        // 隐藏原生搜索框和分页长度选择器，因为我们自定义了这些控件
        dom: "<'row'<'col-sm-12'>>rt<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        // 禁用客户端搜索功能，我们使用服务端搜索
        searching: false,
        language: {
            search: "搜索:",
            // 移除lengthMenu相关设置
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项结果，共 0 项",
            infoFiltered: "(由 _MAX_ 项结果过滤)",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            },
            zeroRecords: "没有匹配结果",
            emptyTable: "暂无数据",
            loadingRecords: "加载中...",
            processing: "处理中..."
        },
        // 配置宽度自动调整，提高订单编号等字段宽度
        autoWidth: true,
        scrollX: true,
        // 添加性能优化选项
        deferRender: true,
        processing: true,
        // 禁用DataTables自带的分页功能，只使用我们自定义的分页控件
        paging: false,
        rowCallback: function(row, data) {
            // 自定义行处理，对产品类型进行增强处理
            if (data) {
                const cells = row.getElementsByTagName('td');
                
                // 尝试查找产品列并增强处理
                for (let i = 0; i < cells.length; i++) {
                    const cell = cells[i];
                    const badge = cell.querySelector('.badge[data-product-type]');
                    
                    // 如果找到产品类型徽章，确保样式正确应用
                    if (badge) {
                        const productType = badge.getAttribute('data-product-type');
                        if (productType === '电商') {
                            badge.style.backgroundColor = '#cfe2ff';
                            badge.style.color = '#084298';
                            badge.style.borderColor = '#b6d4fe';
                        } else if (productType === '租赁') {
                            badge.style.backgroundColor = '#d1e7dd';
                            badge.style.color = '#0f5132';
                            badge.style.borderColor = '#badbcc';
                        }
                    }
                }
            }
            return row;
        }
    });
    
    // 应用样式增强
    if (window.StyleController) {
        StyleController.enhanceTable(tableElement, 'overdue');
    } else {
        // 兼容旧代码
        if (typeof enhanceDataTables === 'function') {
            enhanceDataTables();
        }
    }
    
    // 添加窗口调整大小事件处理
    let resizeTimer;
    $(window).on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            if ($.fn.DataTable.isDataTable(tableElement)) {
                dataTable.columns.adjust().draw(false);
            }
        }, 250);
    });
    
    // 初始调整列宽
    setTimeout(function() {
        if ($.fn.DataTable.isDataTable(tableElement)) {
            dataTable.columns.adjust().draw(false);
        }
    }, 300);
    
    // 表格初始化完成后调整响应式布局
    setTimeout(() => {
        dataTable.columns.adjust().responsive.recalc();
        console.log('逾期订单表格初始化完成');
        
        // 清除加载提示
        if (loadingStatus) {
            loadingStatus.innerHTML = '';
        }
        
        // 自定义控件布局
        customizeTableControls();
    }, 100);
    
    return dataTable;
}

// 自定义表格控件布局
function customizeTableControls() {
    // 获取控件容器
    const tableTop = document.querySelector('.dataTable-top');
    if (!tableTop) return;
    
    // 获取搜索框
    const searchControl = document.querySelector('.dataTables_filter');
    
    if (searchControl) {
        // 修改搜索控件
        const searchLabel = searchControl.querySelector('label');
        if (searchLabel) {
            const inputElement = searchLabel.querySelector('input[type="search"]');
            if (inputElement) {
                inputElement.classList.add('form-control-sm', 'ms-1');
                inputElement.style.maxWidth = '150px';
                inputElement.placeholder = '搜索...';
            }
        }
    }
    
    // 确保顶部控件容器样式正确
    if (tableTop) {
        tableTop.style.display = 'flex';
        tableTop.style.flexWrap = 'nowrap';
        tableTop.style.alignItems = 'center';
        tableTop.style.justifyContent = 'space-between';
        tableTop.style.marginBottom = '1rem';
    }
}

// 表格初始化完成后执行样式调整
$(document).ready(function() {
    // 页面加载完成后执行列宽调整
    $(window).on('load', function() {
        setTimeout(function() {
            if ($.fn.DataTable.isDataTable('#overdueTable')) {
                const table = $('#overdueTable').DataTable();
                table.columns.adjust().draw(false);
            }
        }, 300);
    });
    
    // DOM完全加载后执行
    $(document).on('init.dt', function(e, settings) {
        // 获取DataTable实例所在的容器
        const wrapper = $(settings.nTableWrapper);
        
        // 添加Bootstrap样式到搜索框
        wrapper.find('.dataTables_filter input').addClass('form-control form-control-sm ms-1')
            .css('width', '150px')
            .attr('placeholder', '搜索...');
        
        // 确保搜索框样式正确
        wrapper.find('.dataTables_filter').css({
            'display': 'inline-block',
            'vertical-align': 'middle'
        });
        
        // 调整控件容器
        wrapper.find('.dataTables_filter').parent().css({
            'display': 'flex',
            'align-items': 'center',
            'justify-content': 'flex-end',
            'flex-wrap': 'nowrap',
            'width': '100%'
        });
    });
});
</script>

<!-- 添加下拉选择器事件处理 -->
<script>
// 处理分页大小选择器变更事件
document.addEventListener('DOMContentLoaded', function() {
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            // 构建新的URL，保留当前搜索参数，但重置为第1页
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('page', '1'); // 切换每页条数时重置到第1页
            currentUrl.searchParams.set('limit', this.value);
            // 保留搜索关键词参数，如果存在的话
            const searchQuery = document.getElementById('tableSearch').value;
            if (searchQuery) {
                currentUrl.searchParams.set('search', searchQuery);
            }
            window.location.href = currentUrl.toString();
        });
    }
    
    // 表格搜索功能现在已经通过表单提交方式实现，不再需要客户端处理

    // 如果URL中有搜索参数且搜索框存在，设置搜索框的值
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam) {
        const searchInput = document.getElementById('tableSearch');
        if (searchInput) {
            searchInput.value = searchParam;
        }
    }
});
</script>

<!-- 引入控制器和主应用脚本 -->
<script src="{{ url_for('static', filename='js/controllers/style-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/loading-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/controllers/data-query-controller.js') }}"></script>
<script src="{{ url_for('static', filename='js/app.js') }}"></script>

<!-- 引入主JavaScript文件 -->
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %}
