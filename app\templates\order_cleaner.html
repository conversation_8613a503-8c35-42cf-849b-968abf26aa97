{% extends "base.html" %}

{% block title %}太享查询系统 - 订单清洗{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">订单清洗</h1>
            <div class="h6 text-muted">
                <span class="badge bg-primary">{{ version }}</span>
            </div>
        </div>

        <!-- 订单清洗工具卡片 -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">订单数据清洗工具</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            上传订单文件，系统将根据规则处理数据并生成标准格式的Excel文件。
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ category }}">
                                        {{ message }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="post" enctype="multipart/form-data" class="mb-4">
                            <div class="mb-3">
                                <label for="excelFile" class="form-label">选择Excel文件</label>
                                <div class="input-group">
                                    <input class="form-control" type="file" id="excelFile" name="file" accept=".xlsx,.xls" required style="display: none;">
                                    <input type="text" class="form-control" id="fileNameDisplay" placeholder="未选择文件" readonly>
                                    <button class="btn btn-outline-secondary" type="button" id="uploadBtn">
                                        <i class="bi bi-upload"></i> 上传文件
                                    </button>
                                </div>
                                <div class="form-text">支持.xlsx和.xls格式</div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-arrow-repeat me-1"></i> 开始处理
                            </button>
                        </form>
                        
                        <script>
                            // 文件上传按钮点击事件
                            document.getElementById('uploadBtn').addEventListener('click', function() {
                                document.getElementById('excelFile').click();
                            });
                            
                            // 文件选择后显示文件名
                            document.getElementById('excelFile').addEventListener('change', function() {
                                const fileName = this.files[0] ? this.files[0].name : '未选择文件';
                                document.getElementById('fileNameDisplay').value = fileName;
                            });
                        </script>
                        
                        <div class="mt-4">
                            <h5>数据处理规则说明：</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>输出字段</th>
                                            <th>处理规则</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>日期</td>
                                            <td>使用"起租日期"</td>
                                        </tr>
                                        <tr>
                                            <td>订单编号</td>
                                            <td>使用"订单ID"</td>
                                        </tr>
                                        <tr>
                                            <td>客户姓名</td>
                                            <td>使用"下单姓名"</td>
                                        </tr>
                                        <tr>
                                            <td>型号</td>
                                            <td>使用"商品名称"</td>
                                        </tr>
                                        <tr>
                                            <td>用途</td>
                                            <td>默认填充"自用"</td>
                                        </tr>
                                        <tr>
                                            <td>还款周期</td>
                                            <td>判断"起租日期"与"结束日期"跨度是否大于5个月，如果大于5个月，填充"月还"，否则填充"天还"</td>
                                        </tr>
                                        <tr>
                                            <td>产品</td>
                                            <td>判断"起租日期"与"结束日期"跨度是否大于5个月，如果大于5个月，填充"租赁"，否则填充"电商"</td>
                                        </tr>
                                        <tr>
                                            <td>期数</td>
                                            <td>总期数，再加"期"，"总期数"+"期"</td>
                                        </tr>
                                        <tr>
                                            <td>总待收</td>
                                            <td>当产品为"租赁"时，总代收=总租金+（116*台数）；当产品为"电商"时，总代收=总租金+（119.6*台数）</td>
                                        </tr>
                                        <tr>
                                            <td>备注</td>
                                            <td>使用"用户备注"</td>
                                        </tr>
                                        <tr>
                                            <td>店铺归属</td>
                                            <td>当产品为"租赁"时，为"林林租物"；当产品为"电商"时，为"刚刚好物"</td>
                                        </tr>
                                        <tr>
                                            <td>台数</td>
                                            <td>当产品为"租赁"时，总租金/13644；当产品为"电商"时，总租金/11879.4</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 文件上传相关的JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('excelFile');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        fileInput.addEventListener('change', function() {
            // 检查文件是否已选择
            if (fileInput.files.length > 0) {
                const fileName = fileInput.files[0].name;
                const fileExt = fileName.split('.').pop().toLowerCase();
                
                // 检查文件扩展名
                if (fileExt !== 'xlsx' && fileExt !== 'xls') {
                    alert('请上传Excel文件（.xlsx或.xls格式）');
                    fileInput.value = '';
                }
            }
        });
        
        // 表单提交时显示加载状态
        document.querySelector('form').addEventListener('submit', function() {
            if (fileInput.files.length > 0) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
            }
        });
    });
</script>
{% endblock %}
