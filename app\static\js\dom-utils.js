/**
 * DOM工具类 - 安全的DOM操作和XSS防护
 * 优化性能，减少重复的DOM查询
 */

const DOMUtils = {
    // DOM元素缓存
    cache: new Map(),
    
    // 安全地获取元素
    get(selector, useCache = true) {
        if (useCache && this.cache.has(selector)) {
            const cached = this.cache.get(selector);
            // 验证缓存的元素是否仍在DOM中
            if (document.contains(cached)) {
                return cached;
            } else {
                this.cache.delete(selector);
            }
        }
        
        try {
            const element = document.querySelector(selector);
            if (element && useCache) {
                this.cache.set(selector, element);
            }
            return element;
        } catch (error) {
            console.error('DOMUtils: 无效的选择器:', selector, error);
            return null;
        }
    },
    
    // 获取多个元素
    getAll(selector) {
        try {
            return Array.from(document.querySelectorAll(selector));
        } catch (error) {
            console.error('DOMUtils: 无效的选择器:', selector, error);
            return [];
        }
    },
    
    // 安全地设置文本内容（防XSS）
    setText(element, text) {
        if (!element) return;
        
        if (typeof text !== 'string') {
            text = String(text);
        }
        
        // 使用textContent防止XSS
        element.textContent = text;
    },
    
    // 安全地设置HTML内容
    setHTML(element, html) {
        if (!element) return;
        
        // 清理HTML内容，移除潜在的XSS向量
        const cleanHTML = this.sanitizeHTML(html);
        element.innerHTML = cleanHTML;
    },
    
    // HTML内容清理（基础版本）
    sanitizeHTML(html) {
        if (typeof html !== 'string') {
            return '';
        }
        
        // 创建临时元素来清理HTML
        const temp = document.createElement('div');
        temp.textContent = html;
        
        // 基础的HTML实体编码
        return temp.innerHTML
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    },
    
    // 创建安全的元素
    createElement(tag, options = {}) {
        try {
            const element = document.createElement(tag);
            
            // 设置属性
            if (options.attributes) {
                for (const [key, value] of Object.entries(options.attributes)) {
                    if (this.isSafeAttribute(key)) {
                        element.setAttribute(key, String(value));
                    }
                }
            }
            
            // 设置CSS类
            if (options.className) {
                element.className = String(options.className);
            }
            
            // 设置文本内容
            if (options.text) {
                this.setText(element, options.text);
            }
            
            // 设置HTML内容
            if (options.html) {
                this.setHTML(element, options.html);
            }
            
            return element;
            
        } catch (error) {
            console.error('DOMUtils: 创建元素失败:', error);
            return null;
        }
    },
    
    // 检查属性是否安全
    isSafeAttribute(attr) {
        const dangerousAttrs = [
            'onload', 'onerror', 'onclick', 'onmouseover', 'onfocus',
            'onblur', 'onchange', 'onsubmit', 'onreset', 'onselect',
            'onabort', 'onkeydown', 'onkeypress', 'onkeyup',
            'onmousedown', 'onmouseup', 'onmousemove', 'onmouseout',
            'javascript:', 'vbscript:', 'data:'
        ];
        
        const lowerAttr = attr.toLowerCase();
        return !dangerousAttrs.some(dangerous => 
            lowerAttr.includes(dangerous)
        );
    },
    
    // 批量DOM操作（减少重排重绘）
    batchUpdate(updates) {
        const fragment = document.createDocumentFragment();
        const elementsToUpdate = [];
        
        // 收集所有需要更新的元素
        for (const update of updates) {
            const element = typeof update.element === 'string' ? 
                this.get(update.element) : update.element;
            
            if (element) {
                elementsToUpdate.push({ element, ...update });
            }
        }
        
        // 批量应用更新
        for (const { element, text, html, className, attributes } of elementsToUpdate) {
            if (text !== undefined) {
                this.setText(element, text);
            }
            
            if (html !== undefined) {
                this.setHTML(element, html);
            }
            
            if (className !== undefined) {
                element.className = String(className);
            }
            
            if (attributes) {
                for (const [key, value] of Object.entries(attributes)) {
                    if (this.isSafeAttribute(key)) {
                        element.setAttribute(key, String(value));
                    }
                }
            }
        }
    },
    
    // 检查元素是否可见
    isVisible(element) {
        if (!element) return false;
        
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    },
    
    // 平滑滚动到元素
    scrollToElement(element, options = {}) {
        if (!element) return;
        
        const defaultOptions = {
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        };
        
        element.scrollIntoView({ ...defaultOptions, ...options });
    },
    
    // 获取元素相对于视口的位置
    getElementPosition(element) {
        if (!element) return null;
        
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            bottom: rect.bottom,
            right: rect.right,
            width: rect.width,
            height: rect.height,
            inViewport: rect.top >= 0 && rect.left >= 0 && 
                       rect.bottom <= window.innerHeight && 
                       rect.right <= window.innerWidth
        };
    },
    
    // 清理缓存
    clearCache() {
        this.cache.clear();
        console.log('DOMUtils: 缓存已清理');
    },
    
    // 移除缓存中无效的元素
    cleanCache() {
        for (const [selector, element] of this.cache) {
            if (!document.contains(element)) {
                this.cache.delete(selector);
            }
        }
        console.log('DOMUtils: 缓存已清理无效元素');
    }
};

// 定期清理缓存
if (typeof window !== 'undefined') {
    // 每5分钟清理一次缓存
    setInterval(() => {
        DOMUtils.cleanCache();
    }, 5 * 60 * 1000);
    
    // 页面卸载时清理缓存
    window.addEventListener('beforeunload', () => {
        DOMUtils.clearCache();
    });
    
    // 导出到全局
    window.DOMUtils = DOMUtils;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DOMUtils;
}