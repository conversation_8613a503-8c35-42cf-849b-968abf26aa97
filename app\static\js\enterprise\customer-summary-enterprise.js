/*
 * 太享查询系统 - 客户汇总页面企业级管理器
 * Enterprise Customer Summary Manager
 * Version: 2.0
 * Last Updated: 2024-12-28
 */

/**
 * 企业级客户汇总管理器
 * 负责客户汇总页面的所有交互功能、图表管理、表格增强等
 */
class EnterpriseCustomerSummaryManager {
    constructor() {
        this.charts = new Map();
        this.tables = new Map();
        this.tableInitializationStatus = new Map(); // 跟踪表格初始化状态
        this.isInitialized = false;
        this.config = {
            chartOptions: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 800,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                family: 'Segoe UI'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        }
                    }
                }
            },
            tableOptions: {
                responsive: true,
                scrollX: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                language: {
                    search: '搜索:',
                    lengthMenu: '显示 _MENU_ 条记录',
                    info: '显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项',
                    infoEmpty: '显示第 0 至 0 项结果，共 0 项',
                    infoFiltered: '(由 _MAX_ 项结果过滤)',
                    zeroRecords: '没有匹配的记录',
                    emptyTable: '表中数据为空',
                    loadingRecords: '载入中...',
                    processing: '处理中...',
                    paginate: {
                        first: '首页',
                        previous: '上页',
                        next: '下页',
                        last: '末页'
                    },
                    aria: {
                        sortAscending: ': 以升序排列此列',
                        sortDescending: ': 以降序排列此列'
                    }
                },
                columnDefs: [
                    {
                        targets: 0,
                        className: 'dtr-control',
                        orderable: false,
                        data: null,
                        defaultContent: ''
                    }
                ],
                order: [[1, 'asc']]
            }
        };
        
        console.log('企业级客户汇总管理器已创建');
    }

    /**
     * 异步初始化系统
     */
    async init() {
        if (this.isInitialized) {
            console.log('客户汇总管理器已经初始化，跳过重复初始化');
            return;
        }

        try {
            console.log('开始初始化企业级客户汇总管理器...');
            
            // 检查依赖项
            this.checkDependencies();
            
            // 初始化图表系统
            await this.initCharts();
            
            // 初始化表格系统
            await this.initTables();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 应用数据增强功能
            this.enhanceDataDisplay();
            
            // 设置导出功能
            this.setupExportFeatures();
            
            // 应用响应式优化
            this.applyResponsiveOptimizations();
            
            this.isInitialized = true;
            console.log('企业级客户汇总管理器初始化完成');
            
            // 显示成功通知
            this.showNotification('客户汇总页面加载完成', 'success');
            
        } catch (error) {
            console.error('客户汇总管理器初始化失败:', error);
            this.showNotification('页面初始化失败，请刷新重试', 'error');
            this.handleError(error, '系统初始化');
        }
    }

    /**
     * 检查必要的依赖项
     */
    checkDependencies() {
        const dependencies = [
            { name: 'jQuery', check: () => window.jQuery },
            { name: 'DataTables', check: () => window.jQuery && window.jQuery.fn.DataTable },
            { name: 'Chart.js', check: () => window.Chart },
            { name: 'Bootstrap', check: () => window.bootstrap }
        ];

        const missing = dependencies.filter(dep => !dep.check());
        if (missing.length > 0) {
            const missingNames = missing.map(dep => dep.name).join(', ');
            throw new Error(`缺少必要的依赖项: ${missingNames}`);
        }
        
        console.log('依赖项检查通过');
    }

    /**
     * 初始化图表系统
     */
    async initCharts() {
        console.log('初始化图表系统...');
        
        try {
            // 获取图表数据
            const chartData = this.getChartData();
            
            // 初始化订单金额分布图表
            await this.createOrderAmountChart(chartData);
            
            // 初始化待收金额分布图表
            await this.createReceivableAmountChart(chartData);
            
            // 初始化业务类型分布图表
            await this.createBusinessTypeChart(chartData);
            
            console.log('图表系统初始化完成');
        } catch (error) {
            console.error('图表初始化失败:', error);
            this.showFallbackCharts();
        }
    }

    /**
     * 创建订单金额分布图表
     */
    async createOrderAmountChart(data) {
        const canvas = document.getElementById('orderAmountChart');
        if (!canvas) {
            console.warn('订单金额分布图表容器不存在');
            return;
        }

        const ctx = canvas.getContext('2d');
        const chartData = this.generateOrderAmountData(data);
        
        const chart = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                ...this.config.chartOptions,
                plugins: {
                    ...this.config.chartOptions.plugins,
                    title: {
                        display: true,
                        text: '订单金额分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        this.charts.set('orderAmount', chart);
        console.log('订单金额分布图表创建成功');
    }

    /**
     * 创建待收金额分布图表
     */
    async createReceivableAmountChart(data) {
        const canvas = document.getElementById('receivableAmountChart');
        if (!canvas) {
            console.warn('待收金额分布图表容器不存在');
            return;
        }

        const ctx = canvas.getContext('2d');
        const chartData = this.generateReceivableAmountData(data);
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                ...this.config.chartOptions,
                plugins: {
                    ...this.config.chartOptions.plugins,
                    title: {
                        display: true,
                        text: '待收金额分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    }
                }
            }
        });

        this.charts.set('receivableAmount', chart);
        console.log('待收金额分布图表创建成功');
    }

    /**
     * 创建业务类型分布图表
     */
    async createBusinessTypeChart(data) {
        const canvas = document.getElementById('businessTypeChart');
        if (!canvas) {
            console.warn('业务类型分布图表容器不存在');
            return;
        }

        const ctx = canvas.getContext('2d');
        const chartData = this.generateBusinessTypeData(data);
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                ...this.config.chartOptions,
                plugins: {
                    ...this.config.chartOptions.plugins,
                    title: {
                        display: true,
                        text: '业务类型分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                cutout: '50%'
            }
        });

        this.charts.set('businessType', chart);
        console.log('业务类型分布图表创建成功');
    }

    /**
     * 初始化表格系统
     */
    async initTables() {
        console.log('初始化表格系统...');
        
        // 使用特定的ID选择器，避免重复初始化
        const tableIds = ['receivableTable', 'ordersTable', 'financeTable'];
        const initializedTables = new Set();

        for (const tableId of tableIds) {
            const table = document.getElementById(tableId);
            if (table && !initializedTables.has(tableId)) {
                try {
                    // 检查是否已经被DataTable初始化
                    if ($.fn.DataTable.isDataTable(table)) {
                        console.log(`表格 ${tableId} 已经初始化，先销毁再重新初始化`);
                        $(table).DataTable().destroy();
                        // 清理可能残留的DataTable相关类和属性
                        table.classList.remove('dataTable');
                        table.removeAttribute('role');
                        table.removeAttribute('aria-describedby');
                    }
                    
                    await this.initSingleTable(table, tableId);
                    initializedTables.add(tableId);
                } catch (error) {
                    console.error(`表格 ${tableId} 初始化失败:`, error);
                }
            } else if (!table) {
                console.log(`表格元素 ${tableId} 未找到，跳过初始化`);
            } else {
                console.log(`表格 ${tableId} 已在集合中，跳过重复初始化`);
            }
        }

        console.log('表格系统初始化完成');
    }

    /**
     * 初始化单个表格
     */
    async initSingleTable(table, tableId) {
        if (!table) {
            console.warn(`表格元素为空，跳过初始化: ${tableId}`);
            return;
        }

        try {
            // 检查初始化状态
            if (this.tableInitializationStatus.get(tableId) === 'initialized') {
                console.log(`表格 ${tableId} 已记录为已初始化，跳过重复初始化`);
                return;
            }

            // 检查是否已经被DataTable初始化
            if ($.fn.DataTable.isDataTable(table)) {
                console.log(`表格 ${tableId} 已是DataTable实例，记录状态并跳过初始化`);
                this.tableInitializationStatus.set(tableId, 'initialized');
                return;
            }

            // 标记为正在初始化
            this.tableInitializationStatus.set(tableId, 'initializing');

            // 应用企业级表格样式
            table.classList.add('enterprise-data-table');

            // 检查表格是否有数据行
            const tbody = table.querySelector('tbody');
            const rows = tbody ? tbody.querySelectorAll('tr') : [];

            if (rows.length === 0) {
                console.log(`表格 ${tableId} 无数据行，跳过DataTable初始化`);
                return;
            }

            // 创建DataTable实例配置
            const tableConfig = {
                ...this.config.tableOptions,
                responsive: {
                    details: {
                        type: 'column',
                        target: 0,
                        renderer: function (api, rowIdx, columns) {
                            var data = $.map(columns, function (col, i) {
                                return col.hidden ?
                                    '<tr data-dt-row="' + col.rowIndex + '" data-dt-column="' + col.columnIndex + '">' +
                                    '<td class="dtr-title">' + col.title + ':</td> ' +
                                    '<td class="dtr-data">' + col.data + '</td>' +
                                    '</tr>' :
                                    '';
                            }).join('');

                            return data ? $('<table class="table table-sm"/>').append(data) : false;
                        }
                    }
                },
                columnDefs: [
                    {
                        targets: 0,
                        className: 'dtr-control',
                        orderable: false,
                        data: null,
                        defaultContent: '',
                        responsivePriority: 1
                    },
                    {
                        targets: -1,
                        responsivePriority: 2
                    },
                    {
                        targets: '_all',
                        responsivePriority: 3
                    }
                ],
                initComplete: () => {
                    console.log(`表格 ${tableId} 初始化完成`);
                    this.enhanceTableCells(table);
                    this.setupTableResponsiveFeatures(table, tableId);
                },
                error: function(settings, helpPage, message) {
                    console.error(`表格 ${tableId} DataTable错误:`, message);
                }
            };

            // 等待DOM完全准备
            await new Promise(resolve => setTimeout(resolve, 50));
            
            const dataTable = $(table).DataTable(tableConfig);
            this.tables.set(tableId, dataTable);
            this.tableInitializationStatus.set(tableId, 'initialized');
            console.log(`表格 ${tableId} 创建成功并标记为已初始化`);
            
        } catch (error) {
            console.error(`表格 ${tableId} 初始化失败:`, error);
            this.tableInitializationStatus.set(tableId, 'failed');
            // 应用基础样式作为fallback
            table.classList.add('table', 'table-striped', 'table-hover');
        }
    }

    /**
     * 增强表格单元格功能
     */
    enhanceTableCells(table) {
        const cells = table.querySelectorAll('tbody td:not(.dtr-control)');
        
        cells.forEach(cell => {
            const text = cell.textContent.trim();
            if (text.length > 15) {
                this.wrapCellContent(cell);
            }
            
            // 应用数据类型特定样式
            this.applyCellTypeStyle(cell, text);
        });
    }

    /**
     * 包装单元格内容以支持折叠
     */
    wrapCellContent(cell) {
        const originalText = cell.textContent.trim();
        const wrapper = document.createElement('div');
        wrapper.className = 'cell-content';
        wrapper.textContent = originalText;
        wrapper.title = originalText;

        // 添加点击展开/折叠功能
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleCellExpansion(wrapper);
        });

        // 添加悬停展开功能
        wrapper.addEventListener('mouseenter', () => {
            if (!wrapper.classList.contains('expanded')) {
                this.showCellPreview(wrapper, originalText);
            }
        });

        wrapper.addEventListener('mouseleave', () => {
            this.hideCellPreview(wrapper);
        });

        cell.innerHTML = '';
        cell.appendChild(wrapper);
    }

    /**
     * 切换单元格展开状态
     */
    toggleCellExpansion(wrapper) {
        // 先关闭其他展开的单元格
        const expandedCells = document.querySelectorAll('.cell-content.expanded');
        expandedCells.forEach(cell => {
            if (cell !== wrapper) {
                cell.classList.remove('expanded');
            }
        });

        // 切换当前单元格状态
        wrapper.classList.toggle('expanded');
        
        // 如果展开，确保可见
        if (wrapper.classList.contains('expanded')) {
            this.ensureCellVisible(wrapper);
        }
    }

    /**
     * 显示单元格预览
     */
    showCellPreview(wrapper, text) {
        const preview = document.createElement('div');
        preview.className = 'cell-preview';
        preview.textContent = text;
        preview.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            font-size: 0.875rem;
            line-height: 1.4;
        `;
        
        wrapper.style.position = 'relative';
        wrapper.appendChild(preview);
    }

    /**
     * 隐藏单元格预览
     */
    hideCellPreview(wrapper) {
        const preview = wrapper.querySelector('.cell-preview');
        if (preview) {
            preview.remove();
        }
    }

    /**
     * 确保单元格可见
     */
    ensureCellVisible(wrapper) {
        setTimeout(() => {
            wrapper.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'nearest'
            });
        }, 100);
    }

    /**
     * 应用单元格类型样式
     */
    applyCellTypeStyle(cell, text) {
        // 金额字段
        if (this.isAmountValue(text)) {
            cell.classList.add('amount-cell');
            cell.setAttribute('data-type', 'amount');
        }
        
        // 日期字段
        if (this.isDateValue(text)) {
            cell.classList.add('date-cell');
            cell.setAttribute('data-type', 'date');
        }
        
        // 状态字段
        if (this.isStatusValue(text)) {
            cell.classList.add('status-cell');
            cell.setAttribute('data-type', 'status');
            this.createStatusBadge(cell, text);
        }
    }

    /**
     * 创建状态徽章
     */
    createStatusBadge(cell, text) {
        const badge = document.createElement('span');
        badge.className = 'status-badge';
        badge.textContent = text;
        
        if (text.includes('逾期')) {
            badge.classList.add('status-overdue');
        } else if (text.includes('正常') || text.includes('已还')) {
            badge.classList.add('status-normal');
        } else {
            badge.classList.add('status-business');
        }
        
        cell.innerHTML = '';
        cell.appendChild(badge);
    }

    /**
     * 设置表格响应式功能
     */
    setupTableResponsiveFeatures(table, tableId) {
        // 监听窗口大小变化
        const resizeObserver = new ResizeObserver(entries => {
            this.handleTableResize(table, tableId);
        });
        
        resizeObserver.observe(table.parentElement);
        
        // 设置触摸滑动支持
        this.setupTouchSupport(table);
        
        // 设置键盘导航
        this.setupKeyboardNavigation(table);
    }

    /**
     * 处理表格大小调整
     */
    handleTableResize(table, tableId) {
        const dataTable = this.tables.get(tableId);
        if (dataTable) {
            dataTable.columns.adjust();
            if (dataTable.responsive) {
                dataTable.responsive.recalc();
            }
        }
    }

    /**
     * 设置触摸支持
     */
    setupTouchSupport(table) {
        let startX, startY, startTime;
        
        table.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        });
        
        table.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();
            
            const deltaX = Math.abs(endX - startX);
            const deltaY = Math.abs(endY - startY);
            const deltaTime = endTime - startTime;
            
            // 检测快速滑动手势
            if (deltaTime < 300 && deltaX > 50 && deltaY < 30) {
                // 水平滑动，可以用于切换标签页
                if (endX > startX) {
                    this.switchToPreviousTab();
                } else {
                    this.switchToNextTab();
                }
            }
        });
    }

    /**
     * 设置键盘导航
     */
    setupKeyboardNavigation(table) {
        table.addEventListener('keydown', (e) => {
            // 左右箭头切换标签页
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                if (e.ctrlKey) {
                    e.preventDefault();
                    if (e.key === 'ArrowLeft') {
                        this.switchToPreviousTab();
                    } else {
                        this.switchToNextTab();
                    }
                }
            }
            
            // Enter键展开/折叠单元格
            if (e.key === 'Enter' && e.target.classList.contains('cell-content')) {
                e.preventDefault();
                this.toggleCellExpansion(e.target);
            }
        });
    }

    /**
     * 切换到上一个标签页
     */
    switchToPreviousTab() {
        const tabs = document.querySelectorAll('.enterprise-tab-nav .nav-link');
        const activeTab = document.querySelector('.enterprise-tab-nav .nav-link.active');
        
        if (tabs.length > 0 && activeTab) {
            const currentIndex = Array.from(tabs).indexOf(activeTab);
            const previousIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
            tabs[previousIndex].click();
        }
    }

    /**
     * 切换到下一个标签页
     */
    switchToNextTab() {
        const tabs = document.querySelectorAll('.enterprise-tab-nav .nav-link');
        const activeTab = document.querySelector('.enterprise-tab-nav .nav-link.active');
        
        if (tabs.length > 0 && activeTab) {
            const currentIndex = Array.from(tabs).indexOf(activeTab);
            const nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
            tabs[nextIndex].click();
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        console.log('设置事件监听器...');
        
        // 标签页切换事件
        this.setupTabListeners();
        
        // 窗口调整事件
        this.setupResizeListeners();
        
        // 表格折叠控制
        this.setupTableResponsive();
        
        console.log('事件监听器设置完成');
    }

    /**
     * 设置标签页监听器
     */
    setupTabListeners() {
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);
                
                if (targetPane) {
                    // 重新计算表格布局
                    this.refreshTabContent(targetPane);
                    
                    // 重新计算图表大小
                    this.resizeCharts();
                    
                    // 确保表格完美适配
                    this.ensureTabTableFit(targetPane);
                }
            });
            
            // 添加标签页切换动画
            button.addEventListener('click', (e) => {
                this.animateTabSwitch(e.target);
            });
        });
        
        // 监听来自移动端适配管理器的标签页变化事件
        document.addEventListener('tabShown', (e) => {
            const tabId = e.detail.tabId;
            const targetPane = document.getElementById(tabId);
            
            if (targetPane) {
                // 重新计算表格布局
                this.refreshTabContent(targetPane);
                
                // 重新计算图表大小
                this.resizeCharts();
                
                // 确保表格完美适配
                this.ensureTabTableFit(targetPane);
            }
        });
    }

    /**
     * 确保标签页表格完美适配
     */
    ensureTabTableFit(tabPane) {
        setTimeout(() => {
            const table = tabPane.querySelector('.enterprise-data-table');
            if (table) {
                // 重新计算表格尺寸
                const tableWrapper = table.closest('.table-responsive');
                if (tableWrapper) {
                    // 获取容器宽度
                    const containerWidth = tableWrapper.clientWidth;
                    const tableWidth = table.scrollWidth;
                    
                    // 如果表格宽度超出容器，启用响应式模式
                    if (tableWidth > containerWidth) {
                        this.enableTableResponsiveMode(table);
                    } else {
                        this.disableTableResponsiveMode(table);
                    }
                }
                
                // 重新应用单元格增强
                this.enhanceTableCells(table);
                
                // 确保DataTable响应式功能正常
                const tableId = Array.from(this.tables.keys()).find(id => 
                    this.tables.get(id).table().node() === table
                );
                
                if (tableId) {
                    const dataTable = this.tables.get(tableId);
                    dataTable.columns.adjust();
                    if (dataTable.responsive) {
                        dataTable.responsive.recalc();
                    }
                }
            }
        }, 150);
    }

    /**
     * 启用表格响应式模式
     */
    enableTableResponsiveMode(table) {
        table.classList.add('responsive-enabled');
        
        // 压缩列宽
        const ths = table.querySelectorAll('thead th:not(.dtr-control)');
        ths.forEach(th => {
            th.style.minWidth = '80px';
            th.style.maxWidth = '150px';
        });
        
        // 压缩单元格内容
        const tds = table.querySelectorAll('tbody td:not(.dtr-control)');
        tds.forEach(td => {
            td.style.maxWidth = '120px';
        });
    }

    /**
     * 禁用表格响应式模式
     */
    disableTableResponsiveMode(table) {
        table.classList.remove('responsive-enabled');
        
        // 恢复正常列宽
        const ths = table.querySelectorAll('thead th:not(.dtr-control)');
        ths.forEach(th => {
            th.style.minWidth = '';
            th.style.maxWidth = '';
        });
        
        // 恢复正常单元格
        const tds = table.querySelectorAll('tbody td:not(.dtr-control)');
        tds.forEach(td => {
            td.style.maxWidth = '200px';
        });
    }

    /**
     * 标签页切换动画
     */
    animateTabSwitch(tabButton) {
        // 添加切换动画效果
        const tabNav = tabButton.closest('.enterprise-tab-nav');
        if (tabNav) {
            tabNav.style.transform = 'scale(0.98)';
            setTimeout(() => {
                tabNav.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * 设置窗口调整监听器
     */
    setupResizeListeners() {
        let resizeTimer;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    /**
     * 处理窗口调整
     */
    handleResize() {
        console.log('窗口大小变化，重新优化布局...');
        
        // 重新计算表格布局
        this.refreshAllTables();
        
        // 重新计算图表大小
        this.resizeCharts();
        
        // 应用移动端优化
        this.applyResponsiveOptimizations();
    }

    /**
     * 刷新标签页内容
     */
    refreshTabContent(tabPane) {
        const table = tabPane.querySelector('.enterprise-data-table');
        if (table && $.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();
            dataTable.columns.adjust();
            if (dataTable.responsive) {
                dataTable.responsive.recalc();
            }
        }
    }

    /**
     * 刷新所有表格
     */
    refreshAllTables() {
        this.tables.forEach((dataTable, tableId) => {
            try {
                dataTable.columns.adjust();
                if (dataTable.responsive) {
                    dataTable.responsive.recalc();
                }
            } catch (error) {
                console.error(`表格 ${tableId} 刷新失败:`, error);
            }
        });
    }

    /**
     * 重新调整图表大小
     */
    resizeCharts() {
        this.charts.forEach((chart, chartId) => {
            try {
                chart.resize();
            } catch (error) {
                console.error(`图表 ${chartId} 调整大小失败:`, error);
            }
        });
    }

    /**
     * 设置表格响应式控制
     */
    setupTableResponsive() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('dtr-control')) {
                e.target.classList.toggle('parent');
            }
        });
    }

    /**
     * 应用数据增强功能
     */
    enhanceDataDisplay() {
        console.log('应用数据增强功能...');
        
        // 格式化金额字段
        this.formatAmountFields();
        
        // 格式化日期字段  
        this.formatDateFields();
        
        // 应用状态样式
        this.applyStatusStyles();
        
        console.log('数据增强功能应用完成');
    }

    /**
     * 格式化金额字段
     */
    formatAmountFields() {
        const amountCells = document.querySelectorAll('td');
        
        amountCells.forEach(cell => {
            const text = cell.textContent.trim();
            if (this.isAmountValue(text)) {
                cell.style.textAlign = 'right';
                cell.style.fontFamily = 'Monaco, monospace';
                cell.style.fontWeight = '600';
            }
        });
    }

    /**
     * 判断是否为金额值
     */
    isAmountValue(text) {
        return /^[¥￥]?[\d,]+\.?\d*$/.test(text.replace(/\s/g, ''));
    }

    /**
     * 格式化日期字段
     */
    formatDateFields() {
        const dateCells = document.querySelectorAll('td');
        
        dateCells.forEach(cell => {
            const text = cell.textContent.trim();
            if (this.isDateValue(text)) {
                cell.style.fontFamily = 'Monaco, monospace';
                cell.style.fontSize = '0.875rem';
                cell.style.color = '#64748b';
            }
        });
    }

    /**
     * 判断是否为日期值
     */
    isDateValue(text) {
        return /^\d{4}-\d{2}-\d{2}/.test(text);
    }

    /**
     * 应用状态样式
     */
    applyStatusStyles() {
        const statusCells = document.querySelectorAll('td');
        
        statusCells.forEach(cell => {
            const text = cell.textContent.trim();
            if (this.isStatusValue(text)) {
                this.applyStatusBadge(cell, text);
            }
        });
    }

    /**
     * 判断是否为状态值
     */
    isStatusValue(text) {
        const statusKeywords = ['逾期', '正常', '已还', '未还', '电商', '租赁'];
        return statusKeywords.some(keyword => text.includes(keyword));
    }

    /**
     * 应用状态徽章
     */
    applyStatusBadge(cell, text) {
        const badge = document.createElement('span');
        badge.textContent = text;
        badge.className = 'badge';
        
        if (text.includes('逾期')) {
            badge.classList.add('bg-danger');
        } else if (text.includes('正常') || text.includes('已还')) {
            badge.classList.add('bg-success');
        } else if (text.includes('电商')) {
            badge.classList.add('bg-info');
        } else if (text.includes('租赁')) {
            badge.classList.add('bg-warning');
        } else {
            badge.classList.add('bg-secondary');
        }
        
        cell.innerHTML = '';
        cell.appendChild(badge);
    }

    /**
     * 设置导出功能
     */
    setupExportFeatures() {
        console.log('设置导出功能...');
        
        // 设置图表导出
        this.setupChartExport();
        
        // 设置表格导出
        this.setupTableExport();
        
        console.log('导出功能设置完成');
    }

    /**
     * 设置图表导出
     */
    setupChartExport() {
        window.exportCharts = () => {
            this.charts.forEach((chart, chartId) => {
                try {
                    const canvas = chart.canvas;
                    const link = document.createElement('a');
                    link.download = `${chartId}_图表.png`;
                    link.href = canvas.toDataURL('image/png');
                    link.click();
                } catch (error) {
                    console.error(`导出图表 ${chartId} 失败:`, error);
                }
            });
            
            this.showNotification('图表导出完成', 'success');
        };
    }

    /**
     * 设置表格导出
     */
    setupTableExport() {
        window.exportTables = () => {
            this.tables.forEach((dataTable, tableId) => {
                try {
                    // 这里可以添加表格导出逻辑
                    console.log(`导出表格 ${tableId}`);
                } catch (error) {
                    console.error(`导出表格 ${tableId} 失败:`, error);
                }
            });
        };
    }

    /**
     * 应用响应式优化
     */
    applyResponsiveOptimizations() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            this.applyMobileOptimizations();
        } else {
            this.applyDesktopOptimizations();
        }
    }

    /**
     * 应用移动端优化
     */
    applyMobileOptimizations() {
        console.log('应用移动端优化...');
        
        // 检查是否有移动端适配管理器
        const isMobileManagerActive = window.mobileAdaptiveManager && 
            (window.mobileAdaptiveManager.isMobile || window.mobileAdaptiveManager.isTablet);
        
        if (isMobileManagerActive) {
            // 让移动端适配管理器处理优化
            console.log('移动端适配管理器处理优化');
            return;
        }
        
        // 调整图表高度
        const chartContainers = document.querySelectorAll('.enterprise-chart-wrapper');
        chartContainers.forEach(container => {
            container.style.height = '200px';
        });
        
        // 重新调整图表
        setTimeout(() => {
            this.resizeCharts();
        }, 100);
    }

    /**
     * 应用桌面端优化
     */
    applyDesktopOptimizations() {
        console.log('应用桌面端优化...');
        
        // 调整图表高度
        const chartContainers = document.querySelectorAll('.enterprise-chart-wrapper');
        chartContainers.forEach(container => {
            container.style.height = '250px';
        });
        
        // 重新调整图表
        setTimeout(() => {
            this.resizeCharts();
        }, 100);
    }

    /**
     * 获取图表数据
     */
    getChartData() {
        // 尝试从全局变量获取数据
        if (window.chartData) {
            return window.chartData;
        }
        
        // 尝试从页面元素获取数据
        const dataElement = document.getElementById('chart-data');
        if (dataElement) {
            try {
                return JSON.parse(dataElement.textContent);
            } catch (error) {
                console.error('解析图表数据失败:', error);
            }
        }
        
        // 返回演示数据
        return this.generateDemoData();
    }

    /**
     * 生成演示数据
     */
    generateDemoData() {
        console.log('使用演示数据生成图表');
        
        return {
            orderAmounts: [150000, 200000, 180000, 220000, 190000, 250000],
            receivableAmounts: [50000, 45000, 60000, 55000, 48000, 52000],
            businessTypes: {
                '电商': 60,
                '租赁': 40
            },
            periods: ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期']
        };
    }

    /**
     * 生成订单金额数据
     */
    generateOrderAmountData(data) {
        return {
            labels: data.periods || ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期'],
            datasets: [{
                label: '订单金额',
                data: data.orderAmounts || [150000, 200000, 180000, 220000, 190000, 250000],
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                borderRadius: 4
            }]
        };
    }

    /**
     * 生成待收金额数据
     */
    generateReceivableAmountData(data) {
        return {
            labels: data.periods || ['第1期', '第2期', '第3期', '第4期', '第5期', '第6期'],
            datasets: [{
                label: '待收金额',
                data: data.receivableAmounts || [50000, 45000, 60000, 55000, 48000, 52000],
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                borderColor: 'rgba(16, 185, 129, 1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        };
    }

    /**
     * 生成业务类型数据
     */
    generateBusinessTypeData(data) {
        const businessTypes = data.businessTypes || { '电商': 60, '租赁': 40 };
        
        return {
            labels: Object.keys(businessTypes),
            datasets: [{
                data: Object.values(businessTypes),
                backgroundColor: [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(59, 130, 246, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 2
            }]
        };
    }

    /**
     * 显示后备图表
     */
    showFallbackCharts() {
        console.log('显示后备图表...');
        
        const chartContainers = document.querySelectorAll('.enterprise-chart-wrapper');
        chartContainers.forEach(container => {
            if (!container.querySelector('canvas')) {
                container.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                        <div class="text-center">
                            <i class="bi bi-bar-chart-line" style="font-size: 3rem;"></i>
                            <p class="mt-2 mb-0">图表数据加载中...</p>
                        </div>
                    </div>
                `;
            }
        });
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // 自动消失
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * 错误处理
     */
    handleError(error, context = '未知操作') {
        console.error(`${context} 发生错误:`, error);
        
        // 发送错误报告（如果需要）
        this.reportError(error, context);
        
        // 尝试恢复
        this.attemptRecovery(error, context);
    }

    /**
     * 报告错误
     */
    reportError(error, context) {
        // 这里可以实现错误报告逻辑
        console.log(`错误报告 - 上下文: ${context}, 错误: ${error.message}`);
    }

    /**
     * 尝试恢复
     */
    attemptRecovery(error, context) {
        // 这里可以实现错误恢复逻辑
        console.log(`尝试从错误中恢复 - 上下文: ${context}`);
    }

    /**
     * 销毁管理器
     */
    destroy() {
        console.log('销毁企业级客户汇总管理器...');
        
        // 销毁所有图表
        this.charts.forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts.clear();

        // 销毁所有表格
        this.tables.forEach(dataTable => {
            if (dataTable && typeof dataTable.destroy === 'function') {
                dataTable.destroy();
            }
        });
        this.tables.clear();

        // 清理初始化状态
        this.tableInitializationStatus.clear();

        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);

        this.isInitialized = false;
        console.log('企业级客户汇总管理器已销毁');
    }

    /**
     * 显示订单还款记录
     * 点击订单卡片中的"还款记录"按钮时调用
     */
    showPaymentHistory(buttonElement) {
        try {
            // 获取订单卡片
            const orderCard = buttonElement.closest('.mobile-data-card');
            if (!orderCard) {
                console.error('无法找到订单卡片');
                this.showNotification('无法获取订单信息', 'error');
                return;
            }

            // 提取订单编号
            const orderNumber = this.extractOrderNumber(orderCard);
            if (!orderNumber) {
                console.error('无法提取订单编号');
                this.showNotification('无法获取订单编号', 'error');
                return;
            }

            console.log(`准备显示订单 ${orderNumber} 的还款记录`);

            // 切换到财务流水标签
            this.switchToFinanceTab();

            // 等待标签切换完成后进行过滤
            setTimeout(() => {
                this.filterFinanceRecordsByOrder(orderNumber);
                this.showNotification(`已显示订单 ${orderNumber} 的还款记录`, 'success');
            }, 300);

        } catch (error) {
            console.error('显示还款记录失败:', error);
            this.showNotification('显示还款记录失败，请重试', 'error');
        }
    }

    /**
     * 从订单卡片中提取订单编号
     */
    extractOrderNumber(orderCard) {
        // 方法1: 从标题文本中提取
        const titleElement = orderCard.querySelector('.title-text');
        if (titleElement) {
            const orderNumber = titleElement.textContent.trim();
            if (orderNumber && orderNumber !== '订单编号') {
                return orderNumber;
            }
        }

        // 方法2: 从数据属性中提取（如果有的话）
        const orderNumberAttr = orderCard.getAttribute('data-order-number');
        if (orderNumberAttr) {
            return orderNumberAttr;
        }

        // 方法3: 从订单详情中查找
        const detailElements = orderCard.querySelectorAll('.detail-value');
        for (const element of detailElements) {
            const text = element.textContent.trim();
            if (/^\d{4,}/.test(text)) { // 假设订单编号是4位以上数字开头
                return text;
            }
        }

        return null;
    }

    /**
     * 切换到财务流水标签
     */
    switchToFinanceTab() {
        // 桌面端标签切换
        const financeTab = document.querySelector('#finance-tab');
        if (financeTab) {
            // 使用Bootstrap的标签切换API
            const bsTab = new bootstrap.Tab(financeTab);
            bsTab.show();
        }

        // 移动端标签切换
        const mobileSelect = document.querySelector('#mobileTabSelect');
        if (mobileSelect) {
            mobileSelect.value = 'finance';
            
            // 触发change事件来切换移动端标签
            const changeEvent = new Event('change', { bubbles: true });
            mobileSelect.dispatchEvent(changeEvent);
        }

        // 更新移动端指示器
        this.updateMobileTabIndicator('finance');
    }

    /**
     * 更新移动端标签指示器
     */
    updateMobileTabIndicator(tabId) {
        const indicator = document.querySelector('.mobile-tab-indicator');
        if (!indicator) return;

        const iconElement = indicator.querySelector('.indicator-icon i');
        const textElement = indicator.querySelector('.indicator-text');
        const badgeElement = indicator.querySelector('.indicator-badge');

        if (tabId === 'finance') {
            if (iconElement) iconElement.className = 'bi bi-receipt';
            if (textElement) textElement.textContent = '财务流水';
            if (badgeElement) {
                // 计算财务流水记录数
                const financeCards = document.querySelectorAll('#finance .mobile-data-card[data-category="finance"]');
                badgeElement.textContent = `${financeCards.length}项`;
            }
        }
    }

    /**
     * 根据订单编号过滤财务记录
     */
    filterFinanceRecordsByOrder(orderNumber) {
        // 桌面端表格过滤
        this.filterDesktopFinanceTable(orderNumber);
        
        // 移动端卡片过滤
        this.filterMobileFinanceCards(orderNumber);
        
        // 显示过滤提示
        this.showFilterNotification(orderNumber);
    }

    /**
     * 过滤桌面端财务流水表格
     */
    filterDesktopFinanceTable(orderNumber) {
        const financeTable = this.tables.get('financeTable');
        if (!financeTable) return;

        try {
            // 使用DataTables的搜索功能进行过滤
            financeTable.search(orderNumber).draw();
            console.log(`桌面端表格已过滤显示订单 ${orderNumber} 的记录`);
        } catch (error) {
            console.error('桌面端表格过滤失败:', error);
        }
    }

    /**
     * 过滤移动端财务流水卡片
     */
    filterMobileFinanceCards(orderNumber) {
        const financeCards = document.querySelectorAll('#finance .mobile-data-card[data-category="finance"]');
        let visibleCount = 0;

        financeCards.forEach(card => {
            const referenceValue = card.querySelector('.reference-value');
            if (referenceValue) {
                const cardOrderNumber = referenceValue.textContent.trim();
                const shouldShow = cardOrderNumber === orderNumber;
                
                if (shouldShow) {
                    card.style.display = 'block';
                    card.classList.add('filtered-match');
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                    card.classList.remove('filtered-match');
                }
            }
        });

        // 更新移动端计数
        const badgeElement = document.querySelector('.mobile-tab-indicator .indicator-badge');
        if (badgeElement) {
            badgeElement.textContent = `${visibleCount}项`;
        }

        console.log(`移动端卡片已过滤，显示 ${visibleCount} 条记录`);
    }

    /**
     * 显示过滤提示信息
     */
    showFilterNotification(orderNumber) {
        // 创建过滤提示元素
        const existingNotification = document.querySelector('.finance-filter-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const financeTabPane = document.querySelector('#finance');
        if (!financeTabPane) return;

        const notification = document.createElement('div');
        notification.className = 'finance-filter-notification alert alert-info d-flex justify-content-between align-items-center mb-3';
        notification.innerHTML = `
            <div>
                <i class="bi bi-funnel me-2"></i>
                <strong>当前正在显示:</strong> 订单 ${orderNumber} 的财务流水记录
            </div>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="window.enterpriseCustomerSummaryManager.clearFinanceFilter()">
                    <i class="bi bi-x-circle"></i> 清除过滤
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="this.closest('.finance-filter-notification').remove()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `;

        // 插入到财务流水标签页的开头
        financeTabPane.insertBefore(notification, financeTabPane.firstChild);
    }

    /**
     * 清除财务流水过滤
     */
    clearFinanceFilter() {
        // 清除桌面端表格过滤
        const financeTable = this.tables.get('financeTable');
        if (financeTable) {
            financeTable.search('').draw();
        }

        // 显示所有移动端卡片
        const financeCards = document.querySelectorAll('#finance .mobile-data-card[data-category="finance"]');
        financeCards.forEach(card => {
            card.style.display = 'block';
            card.classList.remove('filtered-match');
        });

        // 更新移动端计数
        const badgeElement = document.querySelector('.mobile-tab-indicator .indicator-badge');
        if (badgeElement) {
            badgeElement.textContent = `${financeCards.length}项`;
        }

        // 移除过滤提示
        const notification = document.querySelector('.finance-filter-notification');
        if (notification) {
            notification.remove();
        }

        this.showNotification('已清除过滤条件，显示所有财务流水', 'success');
        console.log('财务流水过滤已清除');
    }
}

// 全局实例
window.EnterpriseCustomerSummaryManager = EnterpriseCustomerSummaryManager;

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('企业级客户汇总页面DOM加载完成');
    
    // 短延迟确保所有资源加载完成
    setTimeout(async () => {
        try {
            window.enterpriseCustomerSummaryManager = new EnterpriseCustomerSummaryManager();
            await window.enterpriseCustomerSummaryManager.init();
        } catch (error) {
            console.error('企业级客户汇总管理器启动失败:', error);
        }
    }, 500);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.enterpriseCustomerSummaryManager) {
        window.enterpriseCustomerSummaryManager.destroy();
    }
});

// 全局函数，供HTML模板调用
window.showPaymentHistory = function(buttonElement) {
    if (window.enterpriseCustomerSummaryManager) {
        window.enterpriseCustomerSummaryManager.showPaymentHistory(buttonElement);
    } else {
        console.error('企业客户汇总管理器未初始化');
        alert('功能暂时不可用，请刷新页面重试');
    }
}; 