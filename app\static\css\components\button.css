/**
 * 按钮组件 (Button Component)
 * 遵循BEM命名规范
 */

/* ========================================
   按钮基础样式 (.btn)
 ======================================== */

.btn {
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--button-font-weight);
  line-height: var(--line-height-tight);
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  cursor: pointer;
  
  /* 尺寸 */
  padding: var(--spacing-2-5) var(--spacing-4);
  border: var(--border-width-1) solid transparent;
  border-radius: var(--button-border-radius);
  
  /* 过渡效果 */
  transition: var(--button-transition);
  
  /* 禁用状态基础样式 */
  &:disabled,
  &.btn--disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  /* 聚焦状态 */
  &:focus {
    outline: 0;
    box-shadow: var(--shadow-outline);
  }
}

/* ========================================
   按钮尺寸修饰符 (.btn--size)
 ======================================== */

.btn--xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.btn--sm {
  padding: var(--spacing-1-5) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

.btn--lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-md);
}

.btn--xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-xl);
  border-radius: var(--border-radius-lg);
}

/* ========================================
   按钮颜色主题 (.btn--theme)
 ======================================== */

/* 主要按钮 */
.btn--primary {
  color: var(--color-white);
  background-color: var(--color-primary-500);
  border-color: var(--color-primary-500);
  
  &:hover {
    background-color: var(--color-primary-600);
    border-color: var(--color-primary-600);
  }
  
  &:active {
    background-color: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.5);
  }
}

/* 次要按钮 */
.btn--secondary {
  color: var(--color-gray-700);
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-200);
  
  &:hover {
    background-color: var(--color-gray-200);
    border-color: var(--color-gray-300);
  }
  
  &:active {
    background-color: var(--color-gray-300);
    border-color: var(--color-gray-400);
  }
  
  &:focus {
    box-shadow: var(--shadow-outline-gray);
  }
}

/* 成功按钮 */
.btn--success {
  color: var(--color-white);
  background-color: var(--color-success-500);
  border-color: var(--color-success-500);
  
  &:hover {
    background-color: var(--color-success-600);
    border-color: var(--color-success-600);
  }
  
  &:active {
    background-color: var(--color-success-700);
    border-color: var(--color-success-700);
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
  }
}

/* 警告按钮 */
.btn--warning {
  color: var(--color-gray-800);
  background-color: var(--color-warning-500);
  border-color: var(--color-warning-500);
  
  &:hover {
    background-color: var(--color-warning-600);
    border-color: var(--color-warning-600);
  }
  
  &:active {
    background-color: var(--color-warning-700);
    border-color: var(--color-warning-700);
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
  }
}

/* 危险按钮 */
.btn--danger {
  color: var(--color-white);
  background-color: var(--color-danger-500);
  border-color: var(--color-danger-500);
  
  &:hover {
    background-color: var(--color-danger-600);
    border-color: var(--color-danger-600);
  }
  
  &:active {
    background-color: var(--color-danger-700);
    border-color: var(--color-danger-700);
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
  }
}

/* 信息按钮 */
.btn--info {
  color: var(--color-white);
  background-color: var(--color-info-500);
  border-color: var(--color-info-500);
  
  &:hover {
    background-color: var(--color-info-600);
    border-color: var(--color-info-600);
  }
  
  &:active {
    background-color: var(--color-info-700);
    border-color: var(--color-info-700);
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
  }
}

/* ========================================
   按钮样式变体 (.btn--variant)
 ======================================== */

/* 轮廓按钮 */
.btn--outline {
  background-color: transparent;
  
  &.btn--primary {
    color: var(--color-primary-500);
    border-color: var(--color-primary-500);
    
    &:hover {
      color: var(--color-white);
      background-color: var(--color-primary-500);
    }
  }
  
  &.btn--secondary {
    color: var(--color-gray-600);
    border-color: var(--color-gray-300);
    
    &:hover {
      color: var(--color-gray-700);
      background-color: var(--color-gray-100);
    }
  }
  
  &.btn--success {
    color: var(--color-success-500);
    border-color: var(--color-success-500);
    
    &:hover {
      color: var(--color-white);
      background-color: var(--color-success-500);
    }
  }
  
  &.btn--danger {
    color: var(--color-danger-500);
    border-color: var(--color-danger-500);
    
    &:hover {
      color: var(--color-white);
      background-color: var(--color-danger-500);
    }
  }
}

/* 幽灵按钮 */
.btn--ghost {
  background-color: transparent;
  border-color: transparent;
  
  &.btn--primary {
    color: var(--color-primary-500);
    
    &:hover {
      background-color: var(--color-primary-50);
    }
  }
  
  &.btn--secondary {
    color: var(--color-gray-600);
    
    &:hover {
      background-color: var(--color-gray-100);
    }
  }
}

/* 链接样式按钮 */
.btn--link {
  color: var(--color-primary-500);
  background-color: transparent;
  border-color: transparent;
  text-decoration: underline;
  
  &:hover {
    color: var(--color-primary-600);
    text-decoration: none;
  }
  
  &:focus {
    box-shadow: none;
    text-decoration: none;
  }
}

/* ========================================
   按钮形状修饰符 (.btn--shape)
 ======================================== */

.btn--rounded {
  border-radius: var(--border-radius-full);
}

.btn--square {
  border-radius: 0;
}

.btn--pill {
  border-radius: 50px;
}

/* ========================================
   按钮宽度修饰符 (.btn--width)
 ======================================== */

.btn--full {
  width: 100%;
}

.btn--auto {
  width: auto;
}

/* ========================================
   按钮图标样式 (.btn__icon)
 ======================================== */

.btn__icon {
  display: inline-flex;
  align-items: center;
  
  /* 图标在文字前 */
  &--left {
    margin-right: var(--spacing-2);
  }
  
  /* 图标在文字后 */
  &--right {
    margin-left: var(--spacing-2);
  }
  
  /* 只有图标的按钮 */
  &--only {
    margin: 0;
  }
}

/* 只有图标的按钮 */
.btn--icon-only {
  padding: var(--spacing-2-5);
  
  &.btn--xs {
    padding: var(--spacing-1);
  }
  
  &.btn--sm {
    padding: var(--spacing-1-5);
  }
  
  &.btn--lg {
    padding: var(--spacing-3);
  }
  
  &.btn--xl {
    padding: var(--spacing-4);
  }
}

/* ========================================
   按钮加载状态 (.btn--loading)
 ======================================== */

.btn--loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
  
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -0.5em;
    margin-left: -0.5em;
    width: 1em;
    height: 1em;
    border: 0.125em solid transparent;
    border-top-color: currentColor;
    border-radius: var(--border-radius-full);
    animation: btn-loading-spin 0.75s linear infinite;
  }
}

@keyframes btn-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ========================================
   按钮组 (.btn-group)
 ======================================== */

.btn-group {
  display: inline-flex;
  vertical-align: middle;
  
  .btn {
    position: relative;
    flex: 1 1 auto;
    
    /* 移除相邻按钮之间的边框 */
    &:not(:first-child) {
      margin-left: -1px;
    }
    
    /* 调整边框圆角 */
    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
    
    &:first-child:not(:last-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    
    &:last-child:not(:first-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    
    /* 悬停和聚焦时提升层级 */
    &:hover,
    &:focus,
    &:active {
      z-index: 1;
    }
  }
}

/* 垂直按钮组 */
.btn-group--vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  
  .btn {
    width: 100%;
    
    &:not(:first-child) {
      margin-top: -1px;
      margin-left: 0;
    }
    
    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
    
    &:first-child:not(:last-child) {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-top-right-radius: var(--button-border-radius);
    }
    
    &:last-child:not(:first-child) {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border-bottom-left-radius: var(--button-border-radius);
    }
  }
}

/* ========================================
   响应式适配
 ======================================== */

/* ========================================
   侧边栏搜索按钮专门样式 (.sidebar-search)
 ======================================== */

/* 侧边栏搜索按钮基础样式 - 明显边框 */
.sidebar-search .btn-outline-primary {
  border: 2px solid var(--color-primary-500) !important;
  background-color: transparent !important;
  color: var(--color-primary-500) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
}

/* 日期筛选按钮悬停效果 - 蓝色填充 */
.sidebar-search .btn-outline-primary:hover {
  background-color: var(--color-primary-500) !important;
  color: var(--color-white) !important;
  border-color: var(--color-primary-500) !important;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
  transform: translateY(-1px);
}

/* 客户搜索按钮特殊样式 - 绿色主题 */
#customerSearchForm .btn-outline-primary {
  border: 2px solid var(--color-success-500) !important;
  color: var(--color-success-500) !important;
}

#customerSearchForm .btn-outline-primary:hover {
  background-color: var(--color-success-500) !important;
  border-color: var(--color-success-500) !important;
  color: var(--color-white) !important;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* 按钮点击效果 */
.sidebar-search .btn-outline-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

#customerSearchForm .btn-outline-primary:active {
  background-color: var(--color-success-600) !important;
  border-color: var(--color-success-600) !important;
}

/* 按钮聚焦效果 */
.sidebar-search .btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  border-color: var(--color-primary-500) !important;
}

#customerSearchForm .btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

/* 输入框样式优化 - 与按钮保持一致的视觉风格 */
.sidebar-search .form-control-sm {
  border: 2px solid #dee2e6 !important;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.sidebar-search .form-control-sm:focus {
  border-color: var(--color-primary-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
  background-color: #fff !important;
}

/* 客户姓名输入框特殊样式 */
#customerName.form-control-sm:focus {
  border-color: var(--color-success-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15) !important;
}

/* ========================================
   响应式适配
 ======================================== */

@media (max-width: 576px) {
  .btn {
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .btn--lg {
    font-size: var(--font-size-base);
    padding: var(--spacing-2-5) var(--spacing-4);
  }
  
  .btn--xl {
    font-size: var(--font-size-lg);
    padding: var(--spacing-3) var(--spacing-6);
  }
  
  /* 移动端侧边栏搜索按钮适配 */
  .sidebar-search .btn-outline-primary {
    padding: 10px 15px !important;
    font-size: 16px !important;
    border-width: 2px !important;
  }
  
  .sidebar-search .form-control-sm {
    padding: 10px 12px !important;
    font-size: 16px !important;
    height: auto !important;
    border-width: 2px !important;
  }
}