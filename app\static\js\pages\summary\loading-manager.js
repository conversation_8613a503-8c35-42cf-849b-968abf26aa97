/**
 * 加载状态管理模块
 * 负责管理页面各种加载状态，包括全局加载、模块加载和组件加载
 */

class LoadingManager {
    constructor() {
        this.loadingStates = new Map();
        this.globalOverlay = null;
        this.activeLoadings = new Set();
        
        // 绑定方法上下文
        this.showLoading = this.showLoading.bind(this);
        this.hideLoading = this.hideLoading.bind(this);
        this.showGlobalLoading = this.showGlobalLoading.bind(this);
        this.hideGlobalLoading = this.hideGlobalLoading.bind(this);
    }

    /**
     * 初始化加载管理器
     */
    initialize() {
        console.log('LoadingManager: 加载管理器已初始化');
        
        // 创建全局加载覆盖层
        this.createGlobalOverlay();
        
        // 设置全局表单提交加载
        this.setupFormLoadingHandlers();
    }

    /**
     * 创建全局加载覆盖层
     */
    createGlobalOverlay() {
        // 检查是否已存在
        this.globalOverlay = document.getElementById('loadingOverlay');
        
        if (!this.globalOverlay) {
            this.globalOverlay = document.createElement('div');
            this.globalOverlay.id = 'loadingOverlay';
            this.globalOverlay.className = 'loading-overlay';
            this.globalOverlay.innerHTML = `
                <div class="loading-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="loading-text mt-2">正在加载...</div>
                </div>
            `;
            
            // 添加样式
            this.globalOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.9);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            `;
            
            document.body.appendChild(this.globalOverlay);
        }
    }

    /**
     * 显示加载状态
     * @param {string} key - 加载状态标识
     * @param {Object} options - 加载选项
     */
    showLoading(key, options = {}) {
        const config = {
            message: '加载中...',
            element: null,
            global: false,
            spinner: true,
            timeout: 30000, // 30秒超时
            ...options
        };

        console.log(`LoadingManager: 显示加载状态 [${key}]`);

        // 存储加载状态
        this.loadingStates.set(key, config);
        this.activeLoadings.add(key);

        if (config.global) {
            this.showGlobalLoading(config.message);
        } else if (config.element) {
            this.showElementLoading(config.element, config);
        }

        // 设置超时
        if (config.timeout > 0) {
            setTimeout(() => {
                if (this.activeLoadings.has(key)) {
                    console.warn(`LoadingManager: 加载超时 [${key}]`);
                    this.hideLoading(key);
                }
            }, config.timeout);
        }
    }

    /**
     * 隐藏加载状态
     * @param {string} key - 加载状态标识
     */
    hideLoading(key) {
        if (!this.activeLoadings.has(key)) {
            return;
        }

        console.log(`LoadingManager: 隐藏加载状态 [${key}]`);

        const config = this.loadingStates.get(key);
        this.activeLoadings.delete(key);
        this.loadingStates.delete(key);

        if (config) {
            if (config.global) {
                // 检查是否还有其他全局加载
                const hasGlobalLoading = Array.from(this.loadingStates.values())
                    .some(state => state.global);
                
                if (!hasGlobalLoading) {
                    this.hideGlobalLoading();
                }
            } else if (config.element) {
                this.hideElementLoading(config.element);
            }
        }
    }

    /**
     * 显示全局加载
     * @param {string} message - 加载消息
     */
    showGlobalLoading(message = '加载中...') {
        if (this.globalOverlay) {
            const textElement = this.globalOverlay.querySelector('.loading-text');
            if (textElement) {
                textElement.textContent = message;
            }
            
            this.globalOverlay.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 隐藏全局加载
     */
    hideGlobalLoading() {
        if (this.globalOverlay) {
            this.globalOverlay.style.display = 'none';
            document.body.style.overflow = '';
        }
    }

    /**
     * 显示元素加载
     * @param {HTMLElement} element - 目标元素
     * @param {Object} config - 加载配置
     */
    showElementLoading(element, config) {
        if (!element) return;

        // 创建加载覆盖层
        const overlay = document.createElement('div');
        overlay.className = 'element-loading-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        let content = '';
        if (config.spinner) {
            content += `
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
        }
        content += `<span class="loading-message">${config.message}</span>`;

        overlay.innerHTML = content;

        // 确保父元素有相对定位
        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(overlay);
        element.dataset.loadingOverlay = 'true';
    }

    /**
     * 隐藏元素加载
     * @param {HTMLElement} element - 目标元素
     */
    hideElementLoading(element) {
        if (!element) return;

        const overlay = element.querySelector('.element-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
        
        delete element.dataset.loadingOverlay;
    }

    /**
     * 设置表单加载处理器
     */
    setupFormLoadingHandlers() {
        // 为所有表单添加提交加载状态
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.tagName === 'FORM') {
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton) {
                    this.showButtonLoading(submitButton);
                }
                
                // 显示全局加载（如果表单有相应属性）
                if (form.dataset.showGlobalLoading === 'true') {
                    this.showGlobalLoading('正在提交...');
                }
            }
        });
    }

    /**
     * 显示按钮加载状态
     * @param {HTMLElement} button - 按钮元素
     * @param {string} loadingText - 加载文本
     */
    showButtonLoading(button, loadingText = '加载中...') {
        if (!button) return;

        // 保存原始状态
        button.dataset.originalText = button.textContent;
        button.dataset.originalDisabled = button.disabled;

        // 设置加载状态
        button.disabled = true;
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">加载中...</span>
            </span>
            ${loadingText}
        `;
    }

    /**
     * 隐藏按钮加载状态
     * @param {HTMLElement} button - 按钮元素
     */
    hideButtonLoading(button) {
        if (!button) return;

        // 恢复原始状态
        if (button.dataset.originalText) {
            button.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }

        if (button.dataset.originalDisabled !== undefined) {
            button.disabled = button.dataset.originalDisabled === 'true';
            delete button.dataset.originalDisabled;
        }
    }

    /**
     * 显示表格加载状态
     * @param {HTMLElement} table - 表格元素
     */
    showTableLoading(table) {
        if (!table) return;

        const tbody = table.querySelector('tbody');
        if (tbody) {
            const colCount = table.querySelectorAll('thead th').length;
            tbody.innerHTML = `
                <tr>
                    <td colspan="${colCount}" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        正在加载数据...
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 创建进度条
     * @param {string} key - 进度条标识
     * @param {HTMLElement} container - 容器元素
     * @param {Object} options - 选项
     */
    createProgressBar(key, container, options = {}) {
        const config = {
            label: '进度',
            animated: true,
            striped: true,
            ...options
        };

        const progressHTML = `
            <div class="progress-container mb-3">
                <label class="form-label">${config.label}</label>
                <div class="progress">
                    <div class="progress-bar ${config.animated ? 'progress-bar-animated' : ''} ${config.striped ? 'progress-bar-striped' : ''}" 
                         role="progressbar" 
                         style="width: 0%" 
                         aria-valuenow="0" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        0%
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', progressHTML);
        
        const progressElement = container.querySelector('.progress-container:last-child');
        this.loadingStates.set(key, { element: progressElement, type: 'progress' });
        
        return progressElement;
    }

    /**
     * 更新进度条
     * @param {string} key - 进度条标识
     * @param {number} percent - 进度百分比
     */
    updateProgress(key, percent) {
        const state = this.loadingStates.get(key);
        if (state && state.type === 'progress') {
            const progressBar = state.element.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = `${percent}%`;
                progressBar.setAttribute('aria-valuenow', percent);
                progressBar.textContent = `${Math.round(percent)}%`;
            }
        }
    }

    /**
     * 完成进度条
     * @param {string} key - 进度条标识
     */
    completeProgress(key) {
        this.updateProgress(key, 100);
        
        setTimeout(() => {
            const state = this.loadingStates.get(key);
            if (state && state.element) {
                state.element.remove();
                this.loadingStates.delete(key);
            }
        }, 1000);
    }

    /**
     * 检查是否有活动的加载状态
     * @returns {boolean} 是否有活动加载
     */
    hasActiveLoading() {
        return this.activeLoadings.size > 0;
    }

    /**
     * 获取活动加载状态列表
     * @returns {Array} 活动加载状态列表
     */
    getActiveLoadings() {
        return Array.from(this.activeLoadings);
    }

    /**
     * 清除所有加载状态
     */
    clearAllLoading() {
        console.log('LoadingManager: 清除所有加载状态');
        
        // 清除所有活动加载
        this.activeLoadings.forEach(key => {
            this.hideLoading(key);
        });
        
        // 隐藏全局加载
        this.hideGlobalLoading();
        
        // 清除所有按钮加载状态
        document.querySelectorAll('button[data-original-text]').forEach(button => {
            this.hideButtonLoading(button);
        });
    }

    /**
     * 销毁加载管理器
     */
    destroy() {
        this.clearAllLoading();
        
        if (this.globalOverlay && this.globalOverlay.parentNode) {
            this.globalOverlay.parentNode.removeChild(this.globalOverlay);
        }
        
        this.loadingStates.clear();
        this.activeLoadings.clear();
        
        console.log('LoadingManager: 加载管理器已销毁');
    }
}

// 全局函数兼容性
window.showLoading = function() {
    if (window.summaryPageManager && window.summaryPageManager.loadingManager) {
        window.summaryPageManager.loadingManager.showGlobalLoading();
    }
};

window.hideLoading = function() {
    if (window.summaryPageManager && window.summaryPageManager.loadingManager) {
        window.summaryPageManager.loadingManager.hideGlobalLoading();
    }
};

// 导出模块
window.LoadingManager = LoadingManager;