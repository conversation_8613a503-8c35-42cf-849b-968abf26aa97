/**
 * 基础布局样式 - 从base.html提取的内联样式
 * 使用CSS变量系统，提升可维护性
 */

/* 全局样式 */
body {
    font-family: "Microsoft YaHei", sans-serif;
    background-color: var(--color-light);
    overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: var(--shadow-sm);
    background-color: #fff;
    transition: var(--sidebar-transition);
    width: var(--sidebar-width);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: var(--spacing-md);
    text-align: center;
    flex-shrink: 0;
}

.sidebar-header .logo {
    max-width: 80px;
    margin-bottom: var(--spacing-sm);
}

.sidebar-header h4 {
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar .nav {
    flex-grow: 1;
    overflow-y: auto;
}

.sidebar-footer {
    padding: var(--spacing-md);
    text-align: center;
    border-top: 1px solid #eee;
    flex-shrink: 0;
}

#collapseToggle {
    width: 100%;
    margin-top: var(--spacing-sm);
}

.nav-item {
    margin: 2px 0;
}

.nav-link {
    color: #495057;
    border-radius: 0;
    padding: 0.75rem 1rem;
    transition: var(--transition-normal);
}

.nav-link:hover {
    background-color: var(--color-light);
    color: var(--color-primary);
}

.nav-link.active {
    background-color: #e9ecef;
    color: var(--color-primary);
    font-weight: 500;
}

.sidebar-collapsed {
    margin-left: calc(-1 * var(--sidebar-width));
}

/* 侧边栏折叠样式 */
.sidebar-collapsed + .main-content {
    margin-left: 0;
    width: 100%;
}

/* 折叠后的还原按钮 */
.sidebar-expand-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 101;
    display: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    color: #495057;
    box-shadow: var(--shadow-md);
    text-align: center;
    line-height: 40px;
    cursor: pointer;
}

.sidebar-toggle {
    position: fixed;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    z-index: 101;
    display: none;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    box-shadow: var(--shadow-md);
}

.main-content {
    transition: var(--sidebar-transition);
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
}

.main-content-expanded {
    margin-left: 0;
    width: 100%;
}

/* 折叠时的侧边栏样式 */
@media (min-width: 769px) {
    .sidebar-collapsed {
        width: var(--sidebar-width-collapsed);
    }
    
    .sidebar-collapsed .sidebar-header h4,
    .sidebar-collapsed .sidebar-header .user-info,
    .sidebar-collapsed .sidebar-header hr,
    .sidebar-collapsed .nav-text,
    .sidebar-collapsed .sidebar-footer {
        display: none;
    }
    
    .sidebar-collapsed .nav-link {
        text-align: center;
        padding: 0.75rem 0;
    }
    
    .sidebar-collapsed .nav-link .bi {
        margin-right: 0;
        font-size: 1.3rem;
    }
    
    .sidebar-collapsed + .main-content {
        margin-left: var(--sidebar-width-collapsed);
        width: calc(100% - var(--sidebar-width-collapsed));
    }
    
    .sidebar-expand-btn {
        display: none;
    }
    
    .sidebar-collapsed ~ .sidebar-expand-btn {
        display: block;
    }
}

/* 手机设备适配 */
@media (max-width: var(--breakpoint-mobile)) {
    .sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .sidebar-active {
        margin-left: 0;
        box-shadow: var(--shadow-lg);
    }
    
    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 99;
    }
    
    .overlay-active {
        display: block;
    }
    
    /* 移动端隐藏展开按钮 */
    .sidebar-expand-btn {
        display: none;
    }
    
    /* 移动端导航按钮样式 */
    .sidebar-toggle {
        background-color: white;
        border: 1px solid rgba(0,0,0,0.1);
        box-shadow: var(--shadow-sm);
        font-size: 1.2rem;
    }
    
    /* 移动端侧边栏按钮样式优化 */
    .sidebar .nav-link {
        width: 100%;
        text-align: left;
        padding: 0.75rem 1rem;
        margin: 0;
        border-radius: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .sidebar .nav-link .bi {
        width: 24px;
        text-align: center;
        margin-right: var(--spacing-sm);
    }
    
    .sidebar .nav-item {
        width: 100%;
        margin: 0;
    }
    
    .sidebar .btn-link {
        width: 100%;
        text-align: left;
        padding: 0.75rem 1rem;
        margin: 0;
        border-radius: 0;
    }
    
    /* 移动端表格优化 */
    .table-responsive {
        max-width: 100%;
        overflow-x: auto;
    }
    
    /* 移动端页面标题优化 */
    h1.h2 {
        font-size: var(--font-size-xl);
    }
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-modal);
    display: none; /* 默认隐藏 */
}

.loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--color-primary);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-bottom: var(--spacing-sm);
}

.loading-text {
    color: white;
    font-size: var(--font-size-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 数据单元格折叠样式 */
.cell-content {
    position: relative;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    display: block;
    transition: var(--transition-normal);
    color: #333;
}

.cell-content:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 30px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
    pointer-events: none;
}

.cell-content.expanded {
    white-space: normal;
    word-break: break-word;
    max-width: none;
    overflow: visible;
}

.cell-content.expanded:after {
    display: none;
}

/* 鼠标悬停状态 */
.cell-content:hover {
    color: var(--color-primary);
}

/* 自动消失提示 */
.auto-dismiss-alert {
    animation: fadeOut 0.5s ease 3s forwards;
    position: relative;
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; height: 0; padding-top: 0; padding-bottom: 0; margin: 0; }
}

/* DataTables响应式详情样式 */
.dtr-details {
    width: 100%;
}

.dtr-details tr {
    border-bottom: 1px solid #f0f0f0;
}

.dtr-details td {
    padding: var(--spacing-sm);
}

.dtr-title {
    font-weight: bold;
    color: #555;
    min-width: 120px;
}

.dtr-data {
    word-break: break-word;
}

.nav-link .bi {
    margin-right: var(--spacing-sm);
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
}

.sidebar-collapsed .sidebar-header h4,
.sidebar-collapsed .sidebar-header .user-info,
.sidebar-collapsed .sidebar-header hr,
.sidebar-collapsed .nav-text,
.sidebar-collapsed .sidebar-footer {
    display: none;
}

/* 侧边栏折叠时隐藏搜索表单和API状态 */
.sidebar-collapsed .sidebar-search,
.sidebar-collapsed .api-status-container {
    display: none;
}

.sidebar-collapsed .nav-link {
    text-align: center;
    padding: 0.75rem 0;
}

.sidebar-search {
    padding: 0 var(--spacing-md);
    margin-bottom: 0;
}

.sidebar-search form {
    margin-bottom: var(--spacing-sm);
}

.sidebar-search .form-control-sm {
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    background-color: var(--color-light);
    border: 1px solid #dee2e6;
}

.sidebar-search .form-control-sm:focus {
    background-color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

.sidebar-search .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-normal);
}

.sidebar-search .btn-sm:hover {
    background-color: var(--color-primary);
    color: #fff;
    border-color: var(--color-primary);
}

.sidebar-search label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.api-status-container {
    padding: 0 var(--spacing-md) var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* 移动端DataTables控制列样式 */
.dtr-control {
    position: relative;
    cursor: pointer;
    width: 35px;
    min-width: 35px;
    display: table-cell;
}

.dtr-control:before {
    content: '+';
    display: inline-block;
    color: #31b131;
    font-weight: bold;
    font-size: 1.2em;
}

tr.dt-hasChild td.dtr-control:before {
    content: '-';
    color: #d33333;
}

table.dataTable > thead > tr > th.dtr-control,
table.dataTable > tbody > tr > td.dtr-control {
    display: table-cell;
    max-width: 35px;
    min-width: 35px;
    width: 35px;
    padding-right: 5px;
    padding-left: 5px;
}

/* 日期选择器样式 - 确保所有页面都显示日历图标 */
input[type="date"] {
    position: relative;
}

/* 自定义日期选择器图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    cursor: pointer;
}

/* 日期选择器位置调整 */
input[type="date"]::-webkit-datetime-edit {
    padding-right: 28px; /* 为图标留出空间 */
}

/* 兼容性处理 */
input[type="date"]::-webkit-inner-spin-button {
    display: none;
}

input[type="date"]::-webkit-clear-button {
    display: none;
}