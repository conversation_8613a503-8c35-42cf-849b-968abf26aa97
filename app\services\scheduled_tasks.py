"""
定时任务模块 - 实现凌晨2点缓存预热
"""
import logging
import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from flask import current_app

# 导入需要缓存的数据服务函数
from app.services.data_service import get_filtered_data, get_overdue_orders, filter_data
from app import cache

# 配置日志
logger = logging.getLogger(__name__)

def preload_today_data():
    """
    预加载今日数据到缓存
    在凌晨2点执行，提前加载今日待处理订单和逾期订单数据
    """
    try:
        today = datetime.date.today().strftime('%Y-%m-%d')
        logger.info(f"开始预加载今日({today})数据到缓存...")
        
        # 获取今日待处理订单并缓存
        today_orders = get_filtered_data(today)
        logger.info(f"已加载今日待处理订单数据: {len(today_orders.get('results', []))}条记录")
        
        # 存储今日待处理订单数量到长期缓存
        cache.set('daily_today_orders_count', len(today_orders.get('results', [])), timeout=86400)  # 24小时
        cache.set('daily_today_orders_data', today_orders, timeout=86400)  # 24小时
        
        # 获取逾期订单完整数据并缓存 - 修复分页问题
        logger.info("开始获取完整逾期订单数据...")
        
        # 首先获取第一页数据以了解总记录数
        first_page = get_overdue_orders(page=1, limit=100, force_refresh=True)
        
        if 'results' in first_page and 'pagination' in first_page:
            total_records = first_page['pagination'].get('total', 0)
            logger.info(f"从分页信息获取逾期订单总数: {total_records}")
            
            if total_records > 100:
                # 如果总记录数大于100，需要用足够大的limit重新获取全部数据
                logger.info(f"逾期记录数({total_records})较多，使用大limit重新获取全部数据")
                overdue_orders_full = get_overdue_orders(page=1, limit=max(total_records + 100, 10000), force_refresh=True)
                
                # 验证获取到的数据量
                actual_count = len(overdue_orders_full.get('results', []))
                logger.info(f"使用大limit重新获取，实际获取到: {actual_count}条记录")
                
                if actual_count < total_records * 0.9:  # 如果获取到的数据少于总数的90%，说明可能有问题
                    logger.warning(f"获取到的数据量({actual_count})明显少于预期({total_records})，可能存在API限制")
                    # 尝试分批获取
                    logger.info("尝试分批获取逾期订单数据...")
                    all_results = []
                    all_columns = first_page.get('columns', [])
                    
                    # 计算需要的批次数
                    batch_size = 100
                    total_pages = (total_records + batch_size - 1) // batch_size
                    
                    for page_num in range(1, min(total_pages + 1, 51)):  # 限制最多50页，避免过多API调用
                        logger.info(f"获取第{page_num}页数据...")
                        page_data = get_overdue_orders(page=page_num, limit=batch_size, force_refresh=True)
                        
                        if 'results' in page_data and page_data['results']:
                            all_results.extend(page_data['results'])
                            logger.info(f"第{page_num}页获取到{len(page_data['results'])}条记录，累计{len(all_results)}条")
                            
                            # 更新列信息
                            if 'columns' in page_data and page_data['columns']:
                                all_columns = page_data['columns']
                        else:
                            logger.info(f"第{page_num}页没有数据，停止分批获取")
                            break
                    
                    # 构建完整数据结构
                    overdue_orders_full = {
                        'results': all_results,
                        'columns': all_columns,
                        'pagination': {
                            'page': 1,
                            'pages': 1,
                            'total': len(all_results),
                            'limit': len(all_results)
                        }
                    }
                    logger.info(f"分批获取完成，总计获取: {len(all_results)}条记录")
            else:
                # 记录数不多，使用第一页的数据
                overdue_orders_full = first_page
                logger.info(f"逾期记录数较少({total_records})，直接使用第一页数据")
        else:
            # 没有分页信息，使用原始方法
            logger.warning("无法获取分页信息，使用默认大limit方法")
            overdue_orders_full = get_overdue_orders(page=1, limit=10000, force_refresh=True)
        
        # 从完整数据中提取记录数用于统计
        if 'results' in overdue_orders_full:
            total_overdue_count = len(overdue_orders_full['results'])
            logger.info(f"最终加载的逾期订单数据: {total_overdue_count}条记录")
            
            # 存储逾期订单总数到长期缓存（用于首页显示）
            cache.set('daily_overdue_orders_count', total_overdue_count, timeout=86400)  # 24小时
            
            # 存储完整逾期订单数据到长期缓存
            cache.set('daily_overdue_orders_data', overdue_orders_full, timeout=86400)  # 24小时
            
            # 记录一些统计信息
            if total_overdue_count > 0:
                logger.info(f"逾期订单缓存更新成功 - 总数: {total_overdue_count}")
                # 简单统计一下数据分布
                if 'results' in overdue_orders_full and overdue_orders_full['results']:
                    sample_keys = list(overdue_orders_full['results'][0].keys()) if overdue_orders_full['results'] else []
                    logger.info(f"数据字段: {sample_keys[:10]}...")  # 只记录前10个字段
        else:
            logger.warning("逾期订单数据格式异常，未能获取到results字段")
            cache.set('daily_overdue_orders_count', 0, timeout=86400)
            cache.set('daily_overdue_orders_data', {'results': [], 'columns': []}, timeout=86400)
        
        logger.info("数据预加载完成，缓存将保持24小时有效")
        
    except Exception as e:
        logger.error(f"预加载数据到缓存时出错: {str(e)}", exc_info=True)


def init_scheduler(app):
    """
    初始化定时任务调度器
    在应用启动时调用此函数
    """
    with app.app_context():
        scheduler = BackgroundScheduler()
        
        # 添加每天凌晨2点执行的任务
        scheduler.add_job(
            preload_today_data,
            trigger=CronTrigger(hour=2, minute=0),
            id='preload_daily_data',
            replace_existing=True,
            max_instances=1
        )
        
        # 启动调度器
        scheduler.start()
        logger.info("定时任务调度器已启动，将在每天凌晨2点预加载数据")
        
        # 应用启动时立即执行一次数据预加载
        preload_today_data()
        
        # 将调度器附加到应用，以便在应用上下文中可用
        app.scheduler = scheduler
        
        return scheduler
